# TreeDash Log

**Subtitle:** Forest Running & Obstacle Course Tracking Assistant

A local data recording tool designed specifically for forest parkour, obstacle course training, and free climbing activities. Emphasizes action path recording, obstacle completion analysis, and multi-attempt comparisons.

## Features

### Module 1: Route Logger

The core functionality for recording each forest traversal or obstacle run with detailed path, timing, action types, and failure points.

### Module 2: Obstacle Efficiency Calculator

Advanced performance analysis tool that evaluates action efficiency, time distribution, and failure risk points to provide actionable insights.

#### Key Features:
- **Route Management**: Create and edit routes with custom names, locations, start/end points
- **Action Node Tracking**: Record individual actions (jump, roll, climb, hang, vault, balance, slide, swing, crawl)
- **Completion Status**: Track success, failure, delayed, or not attempted for each action
- **Timer Integration**: Built-in timer for tracking training duration
- **Difficulty Levels**: 5-level difficulty system (Beginner to Expert)
- **Progress Visualization**: Visual progress bars and completion percentages
- **Notes & Documentation**: Add failure notes and observations

#### Action Types Supported:
- **Jump** - Jumping movements
- **Roll** - Rolling techniques
- **Climb** - Climbing actions
- **Hang** - Hanging exercises
- **Vault** - Vaulting movements
- **Balance** - Balance challenges
- **Slide** - Sliding techniques
- **Swing** - Swinging movements
- **Crawl** - Crawling exercises
- **Other** - Custom actions

#### Efficiency Calculator Features:
- **Performance Analysis**: Comprehensive efficiency scoring based on success rate, time, and consistency
- **Action Performance Breakdown**: Individual analysis of each action type with failure patterns
- **Risk Assessment**: Intelligent risk level evaluation with mitigation strategies
- **Improvement Suggestions**: AI-powered recommendations based on performance data
- **Success Rate Curve**: Visual representation of improvement trajectory over time
- **Time Distribution Analysis**: Identify time-consuming actions and bottlenecks
- **Historical Data Integration**: Compare performance across multiple attempts
- **Quick Analysis**: One-tap analysis directly from route details

#### Analysis Outputs:
- **Efficiency Score**: Overall performance rating (0-100%)
- **Success Rate**: Percentage of successful action completions
- **Risk Level**: Low, Moderate, High, or Critical risk assessment
- **Most Problematic Actions**: Identification of highest failure rate actions
- **Time-Consuming Actions**: Actions taking the most time
- **Improvement Recommendations**: Prioritized suggestions for technique, timing, strength, flexibility, and mental focus
- **Performance Trends**: Visual charts showing improvement over time

## Technical Details

### Architecture
- **Language**: Swift
- **UI Framework**: UIKit with SnapKit for Auto Layout
- **Data Persistence**: UserDefaults (JSON encoding)
- **Design Pattern**: MVC (Model-View-Controller)

### Dependencies
- **SnapKit**: Auto Layout DSL
- **IQKeyboardManagerSwift**: Keyboard management
- **AAInfographics**: Charts and graphs (for future analytics)

### Key Components

#### Models
- `Route`: Main route data model with action nodes
- `ActionNode`: Individual action within a route
- `RouteManager`: Data persistence and management

#### Views
- `RouteTableViewCell`: Enhanced route display with gradients and animations
- `ActionNodeTableViewCell`: Action display with status indicators
- `ActionTypeCollectionViewCell`: Action type selection

#### Controllers
- `RouteListViewController`: Main route listing with search and filter
- `RouteDetailViewController`: Route creation/editing with timer and quick analysis
- `ActionDetailViewController`: Individual action management
- `EfficiencyCalculatorViewController`: Main calculator interface with route selection
- `PerformanceAnalyticsViewController`: Detailed analytics and insights display
- `SuccessRateCurveViewController`: Visual improvement trajectory charts
- `AnalyticsHistoryViewController`: Historical analysis results management

### UI Design
- **Theme**: Purple-based color scheme
- **Style**: Modern card-based design with shadows and gradients
- **Animations**: Subtle animations for better user experience
- **Icons**: System SF Symbols for consistency

## Installation

1. Clone the repository
2. Open `TreeDashLog.xcworkspace` in Xcode
3. Build and run on iOS Simulator or device

## Usage

### Route Logger
1. **Create a Route**: Tap the "+" button to create a new route
2. **Add Route Details**: Fill in route name, location, start/end points
3. **Set Difficulty**: Choose from 5 difficulty levels
4. **Use Timer**: Start/stop timer during training
5. **Add Actions**: Add individual action nodes with completion status
6. **Track Progress**: View completion rates and statistics
7. **Add Notes**: Document failures and observations

### Efficiency Calculator
1. **Select Route**: Choose a route from your logged routes
2. **Configure Analysis**: Enable/disable historical data inclusion
3. **Run Analysis**: Tap "Analyze Performance" to generate insights
4. **Review Results**: Examine efficiency scores, risk levels, and suggestions
5. **View Success Curve**: Track improvement trajectory over time
6. **Apply Recommendations**: Follow suggested improvements for better performance
7. **Quick Analysis**: Use the chart icon in route details for instant analysis

## Future Enhancements

- Statistics and analytics dashboard
- Route sharing and export
- Photo/video attachments
- GPS tracking integration
- Social features for team training
- Advanced filtering and search
- Data export capabilities

## License

This project is for educational and personal use.
