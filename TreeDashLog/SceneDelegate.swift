//
//  SceneDelegate.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import IQKeyboardManagerSwift
import IQKeyboardToolbarManager

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let windowScene = (scene as? UIWindowScene) else { return }
        IQKeyboardManager.shared.isEnabled = true
        IQKeyboardToolbarManager.shared.isEnabled = true
        window = UIWindow(windowScene: windowScene)

        // Set up tab bar controller with both modules
        let tabBarController = UITabBarController()

        // Route Logger Tab
        let routeListVC = RouteListViewController()
        let routeNavController = UINavigationController(rootViewController: routeListVC)
        routeNavController.tabBarItem = UITabBarItem(
            title: "Routes",
            image: UIImage(systemName: "figure.run"),
            selectedImage: UIImage(systemName: "figure.run.circle.fill")
        )

        // Efficiency Calculator Tab
        let calculatorVC = EfficiencyCalculatorViewController()
        let calculatorNavController = UINavigationController(rootViewController: calculatorVC)
        calculatorNavController.tabBarItem = UITabBarItem(
            title: "Calculator",
            image: UIImage(systemName: "chart.bar.xaxis"),
            selectedImage: UIImage(systemName: "chart.bar.xaxis.fill")
        )

        tabBarController.viewControllers = [routeNavController, calculatorNavController]

        // Apply purple theme
        setupNavigationBarAppearance()
        setupTabBarAppearance()

        window?.rootViewController = tabBarController
        window?.makeKeyAndVisible()
    }
    
    private func setupNavigationBarAppearance() {
        let appearance = UINavigationBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.systemPurple
        appearance.titleTextAttributes = [.foregroundColor: UIColor.white]
        appearance.largeTitleTextAttributes = [.foregroundColor: UIColor.white]

        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance
        UINavigationBar.appearance().compactAppearance = appearance
        UINavigationBar.appearance().tintColor = UIColor.white
    }

    private func setupTabBarAppearance() {
        let tabBarAppearance = UITabBarAppearance()
        tabBarAppearance.configureWithOpaqueBackground()
        tabBarAppearance.backgroundColor = .systemBackground

        // Selected item color
        tabBarAppearance.stackedLayoutAppearance.selected.iconColor = .systemPurple
        tabBarAppearance.stackedLayoutAppearance.selected.titleTextAttributes = [.foregroundColor: UIColor.systemPurple]

        // Normal item color
        tabBarAppearance.stackedLayoutAppearance.normal.iconColor = .systemGray
        tabBarAppearance.stackedLayoutAppearance.normal.titleTextAttributes = [.foregroundColor: UIColor.systemGray]

        UITabBar.appearance().standardAppearance = tabBarAppearance
        UITabBar.appearance().scrollEdgeAppearance = tabBarAppearance
    }

    func sceneDidDisconnect(_ scene: UIScene) {}
    func sceneDidBecomeActive(_ scene: UIScene) {}
    func sceneWillResignActive(_ scene: UIScene) {}
    func sceneWillEnterForeground(_ scene: UIScene) {}
    func sceneDidEnterBackground(_ scene: UIScene) {}
}
