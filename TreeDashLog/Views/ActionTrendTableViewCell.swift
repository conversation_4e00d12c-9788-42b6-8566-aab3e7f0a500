//
//  ActionTrendTableViewCell.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class ActionTrendTableViewCell: UITableViewCell {
    static let identifier = "ActionTrendTableViewCell"
    
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 8
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.systemGray5.cgColor
        return view
    }()
    
    private let actionIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemPurple
        imageView.backgroundColor = .systemPurple.withAlphaComponent(0.1)
        imageView.layer.cornerRadius = 16
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    private let actionNameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = .label
        return label
    }()
    
    private let successRateLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let averageTimeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let trendIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let trendLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .bold)
        label.textAlignment = .center
        return label
    }()
    
    private let chevronImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        
        [actionIconImageView, actionNameLabel, successRateLabel, averageTimeLabel,
         trendIconImageView, trendLabel, chevronImageView].forEach {
            containerView.addSubview($0)
        }
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 0, bottom: 4, right: 0))
        }
        
        actionIconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        actionNameLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().inset(8)
            make.trailing.equalTo(trendIconImageView.snp.leading).offset(-8)
        }
        
        successRateLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalTo(actionNameLabel.snp.bottom).offset(2)
        }
        
        averageTimeLabel.snp.makeConstraints { make in
            make.leading.equalTo(successRateLabel.snp.trailing).offset(12)
            make.centerY.equalTo(successRateLabel)
            make.bottom.equalToSuperview().inset(8)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        trendIconImageView.snp.makeConstraints { make in
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-12)
            make.top.equalToSuperview().inset(8)
            make.width.height.equalTo(20)
        }
        
        trendLabel.snp.makeConstraints { make in
            make.centerX.equalTo(trendIconImageView)
            make.top.equalTo(trendIconImageView.snp.bottom).offset(2)
            make.bottom.equalToSuperview().inset(8)
            make.width.equalTo(60)
        }
    }
    
    // MARK: - Configuration
    func configure(with performance: ActionPerformanceAnalysis) {
        actionIconImageView.image = UIImage(systemName: performance.actionType.systemIcon)
        actionNameLabel.text = performance.actionType.displayName
        
        let successPercentage = Int(performance.successRate * 100)
        successRateLabel.text = "\(successPercentage)% success"
        
        averageTimeLabel.text = "avg \(performance.formattedAverageTime)"
        
        // Trend configuration
        trendIconImageView.image = UIImage(systemName: performance.trend.systemIcon)
        trendIconImageView.tintColor = performance.trend.color
        trendLabel.text = performance.trend.displayName
        trendLabel.textColor = performance.trend.color
        
        // Update success rate color
        if performance.successRate >= 0.8 {
            successRateLabel.textColor = .systemGreen
        } else if performance.successRate >= 0.5 {
            successRateLabel.textColor = .systemOrange
        } else {
            successRateLabel.textColor = .systemRed
        }
        
        // Add trend animation
        addTrendAnimation(for: performance.trend)
    }
    
    private func addTrendAnimation(for trend: PerformanceTrend) {
        switch trend {
        case .improving:
            let animation = CABasicAnimation(keyPath: "transform.translation.y")
            animation.fromValue = 2
            animation.toValue = -2
            animation.duration = 1.0
            animation.autoreverses = true
            animation.repeatCount = .infinity
            trendIconImageView.layer.add(animation, forKey: "improvingAnimation")
            
        case .declining:
            let animation = CABasicAnimation(keyPath: "transform.translation.y")
            animation.fromValue = -2
            animation.toValue = 2
            animation.duration = 1.0
            animation.autoreverses = true
            animation.repeatCount = .infinity
            trendIconImageView.layer.add(animation, forKey: "decliningAnimation")
            
        case .stable:
            let animation = CABasicAnimation(keyPath: "opacity")
            animation.fromValue = 1.0
            animation.toValue = 0.7
            animation.duration = 1.5
            animation.autoreverses = true
            animation.repeatCount = .infinity
            trendIconImageView.layer.add(animation, forKey: "stableAnimation")
            
        case .insufficient_data:
            let animation = CABasicAnimation(keyPath: "transform.rotation")
            animation.fromValue = 0
            animation.toValue = Double.pi * 2
            animation.duration = 2.0
            animation.repeatCount = .infinity
            trendIconImageView.layer.add(animation, forKey: "insufficientDataAnimation")
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        actionIconImageView.image = nil
        actionNameLabel.text = nil
        successRateLabel.text = nil
        averageTimeLabel.text = nil
        trendIconImageView.image = nil
        trendLabel.text = nil
        trendIconImageView.layer.removeAllAnimations()
    }
}
