//
//  ActionTypeCollectionViewCell.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class ActionTypeCollectionViewCell: UICollectionViewCell {
    static let identifier = "ActionTypeCollectionViewCell"
    
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.borderWidth = 2
        view.layer.borderColor = UIColor.systemGray5.cgColor
        return view
    }()
    
    private let iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemGray
        return imageView
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 10, weight: .medium)
        label.textColor = .systemGray
        label.textAlignment = .center
        label.numberOfLines = 1
        label.adjustsFontSizeToFitWidth = true
        label.minimumScaleFactor = 0.8
        return label
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        contentView.addSubview(containerView)
        
        [iconImageView, titleLabel].forEach {
            containerView.addSubview($0)
        }
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        iconImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(8)
            make.width.height.equalTo(32)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(4)
            make.bottom.equalToSuperview().inset(4)
        }
    }
    
    // MARK: - Configuration
    func configure(with actionType: ActionType, isSelected: Bool) {
        iconImageView.image = UIImage(systemName: actionType.systemIcon)
        titleLabel.text = actionType.displayName
        
        if isSelected {
            containerView.backgroundColor = .systemPurple
            containerView.layer.borderColor = UIColor.systemPurple.cgColor
            iconImageView.tintColor = .white
            titleLabel.textColor = .white
            
            // Add subtle animation
            UIView.animate(withDuration: 0.2) {
                self.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
            }
        } else {
            containerView.backgroundColor = .systemBackground
            containerView.layer.borderColor = UIColor.systemGray5.cgColor
            iconImageView.tintColor = .systemGray
            titleLabel.textColor = .systemGray
            
            UIView.animate(withDuration: 0.2) {
                self.transform = .identity
            }
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        iconImageView.image = nil
        titleLabel.text = nil
        transform = .identity
        containerView.backgroundColor = .systemBackground
        containerView.layer.borderColor = UIColor.systemGray5.cgColor
        iconImageView.tintColor = .systemGray
        titleLabel.textColor = .systemGray
    }
}
