//
//  ActionNodeTableViewCell.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class ActionNodeTableViewCell: UITableViewCell {
    static let identifier = "ActionNodeTableViewCell"
    
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.borderWidth = 2
        view.layer.borderColor = UIColor.systemGray5.cgColor
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        view.layer.shadowOpacity = 0.1
        return view
    }()

    private let statusIndicator: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 4
        return view
    }()
    
    private let actionIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemPurple
        imageView.backgroundColor = .systemPurple.withAlphaComponent(0.1)
        imageView.layer.cornerRadius = 20
        imageView.layer.masksToBounds = true
        return imageView
    }()

    private let actionNameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 17, weight: .bold)
        label.textColor = .label
        return label
    }()

    private let actionTypeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let statusIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        imageView.layer.cornerRadius = 12
        imageView.layer.masksToBounds = true
        return imageView
    }()

    private let statusLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 11, weight: .bold)
        label.textAlignment = .center
        return label
    }()

    private let chevronImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)

        [statusIndicator, actionIconImageView, actionNameLabel, actionTypeLabel,
         statusIconImageView, statusLabel, chevronImageView].forEach {
            containerView.addSubview($0)
        }
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 0, bottom: 4, right: 0))
        }

        statusIndicator.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
            make.width.equalTo(4)
        }

        actionIconImageView.snp.makeConstraints { make in
            make.leading.equalTo(statusIndicator.snp.trailing).offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        actionNameLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().inset(10)
            make.trailing.equalTo(statusIconImageView.snp.leading).offset(-12)
        }

        actionTypeLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalTo(actionNameLabel.snp.bottom).offset(2)
            make.trailing.equalTo(statusIconImageView.snp.leading).offset(-12)
            make.bottom.equalToSuperview().inset(10)
        }

        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(18)
        }

        statusIconImageView.snp.makeConstraints { make in
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-12)
            make.top.equalToSuperview().inset(8)
            make.width.height.equalTo(24)
        }

        statusLabel.snp.makeConstraints { make in
            make.centerX.equalTo(statusIconImageView)
            make.top.equalTo(statusIconImageView.snp.bottom).offset(2)
            make.bottom.equalToSuperview().inset(8)
            make.width.equalTo(60)
        }
    }
    
    // MARK: - Configuration
    func configure(with actionNode: ActionNode) {
        actionIconImageView.image = UIImage(systemName: actionNode.type.systemIcon)
        actionNameLabel.text = actionNode.name
        actionTypeLabel.text = actionNode.type.displayName

        statusIconImageView.image = UIImage(systemName: actionNode.completionStatus.systemIcon)
        statusIconImageView.tintColor = actionNode.completionStatus.color
        statusLabel.text = actionNode.completionStatus.displayName
        statusLabel.textColor = actionNode.completionStatus.color

        // Update status indicator and container styling based on status
        statusIndicator.backgroundColor = actionNode.completionStatus.color

        switch actionNode.completionStatus {
        case .success:
            containerView.layer.borderColor = UIColor.systemGreen.cgColor
            containerView.backgroundColor = UIColor.systemGreen.withAlphaComponent(0.05)
            addSuccessAnimation()
        case .failed:
            containerView.layer.borderColor = UIColor.systemRed.cgColor
            containerView.backgroundColor = UIColor.systemRed.withAlphaComponent(0.05)
        case .delayed:
            containerView.layer.borderColor = UIColor.systemOrange.cgColor
            containerView.backgroundColor = UIColor.systemOrange.withAlphaComponent(0.05)
        case .notAttempted:
            containerView.layer.borderColor = UIColor.systemGray5.cgColor
            containerView.backgroundColor = .systemBackground
        }

        // Add subtle scale animation on configuration
        UIView.animate(withDuration: 0.2, delay: 0, options: [.curveEaseOut], animations: {
            self.transform = CGAffineTransform(scaleX: 1.02, y: 1.02)
        }) { _ in
            UIView.animate(withDuration: 0.2) {
                self.transform = .identity
            }
        }
    }

    private func addSuccessAnimation() {
        let pulseAnimation = CABasicAnimation(keyPath: "transform.scale")
        pulseAnimation.fromValue = 1.0
        pulseAnimation.toValue = 1.05
        pulseAnimation.duration = 0.6
        pulseAnimation.autoreverses = true
        pulseAnimation.repeatCount = 2
        statusIconImageView.layer.add(pulseAnimation, forKey: "successPulse")
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        actionIconImageView.image = nil
        actionNameLabel.text = nil
        actionTypeLabel.text = nil
        statusIconImageView.image = nil
        statusLabel.text = nil
        containerView.layer.borderColor = UIColor.systemGray5.cgColor
        containerView.backgroundColor = .systemBackground
        statusIndicator.backgroundColor = .systemGray5
        statusIconImageView.layer.removeAllAnimations()
        transform = .identity
    }
}
