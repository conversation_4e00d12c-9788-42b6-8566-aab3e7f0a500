//
//  ActionNodeTableViewCell.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class ActionNodeTableViewCell: UITableViewCell {
    static let identifier = "ActionNodeTableViewCell"
    
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 8
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.systemGray5.cgColor
        return view
    }()
    
    private let actionIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemPurple
        return imageView
    }()
    
    private let actionNameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private let actionTypeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let statusIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let statusLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        return label
    }()
    
    private let chevronImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        
        [actionIconImageView, actionNameLabel, actionTypeLabel, 
         statusIconImageView, statusLabel, chevronImageView].forEach {
            containerView.addSubview($0)
        }
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 0, bottom: 4, right: 0))
        }
        
        actionIconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        actionNameLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().inset(8)
            make.trailing.equalTo(statusIconImageView.snp.leading).offset(-8)
        }
        
        actionTypeLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalTo(actionNameLabel.snp.bottom).offset(2)
            make.trailing.equalTo(statusIconImageView.snp.leading).offset(-8)
            make.bottom.equalToSuperview().inset(8)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        statusIconImageView.snp.makeConstraints { make in
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
            make.top.equalToSuperview().inset(8)
            make.width.height.equalTo(20)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
            make.top.equalTo(statusIconImageView.snp.bottom).offset(2)
            make.bottom.equalToSuperview().inset(8)
        }
    }
    
    // MARK: - Configuration
    func configure(with actionNode: ActionNode) {
        actionIconImageView.image = UIImage(systemName: actionNode.type.systemIcon)
        actionNameLabel.text = actionNode.name
        actionTypeLabel.text = actionNode.type.displayName
        
        statusIconImageView.image = UIImage(systemName: actionNode.completionStatus.systemIcon)
        statusIconImageView.tintColor = actionNode.completionStatus.color
        statusLabel.text = actionNode.completionStatus.displayName
        statusLabel.textColor = actionNode.completionStatus.color
        
        // Update container border color based on status
        switch actionNode.completionStatus {
        case .success:
            containerView.layer.borderColor = UIColor.systemGreen.cgColor
        case .failed:
            containerView.layer.borderColor = UIColor.systemRed.cgColor
        case .delayed:
            containerView.layer.borderColor = UIColor.systemOrange.cgColor
        case .notAttempted:
            containerView.layer.borderColor = UIColor.systemGray5.cgColor
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        actionIconImageView.image = nil
        actionNameLabel.text = nil
        actionTypeLabel.text = nil
        statusIconImageView.image = nil
        statusLabel.text = nil
        containerView.layer.borderColor = UIColor.systemGray5.cgColor
    }
}
