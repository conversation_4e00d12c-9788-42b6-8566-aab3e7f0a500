//
//  RouteTableViewCell.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class RouteTableViewCell: UITableViewCell {
    static let identifier = "RouteTableViewCell"
    
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.systemPurple.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        view.layer.shadowOpacity = 0.15
        return view
    }()

    private let gradientLayer: CAGradientLayer = {
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor.systemPurple.withAlphaComponent(0.1).cgColor,
            UIColor.clear.cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0)
        gradient.endPoint = CGPoint(x: 1, y: 1)
        gradient.cornerRadius = 16
        return gradient
    }()

    private let accentBar: UIView = {
        let view = UIView()
        view.backgroundColor = .systemPurple
        view.layer.cornerRadius = 2
        return view
    }()
    
    private let routeNumberLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .bold)
        label.textColor = .white
        label.backgroundColor = .systemPurple
        label.textAlignment = .center
        label.layer.cornerRadius = 8
        label.layer.masksToBounds = true
        return label
    }()

    private let routeNameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 20, weight: .bold)
        label.textColor = .label
        label.numberOfLines = 1
        return label
    }()

    private let locationLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 15, weight: .medium)
        label.textColor = .secondaryLabel
        label.numberOfLines = 1
        return label
    }()
    
    private let locationIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "location")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let difficultyView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 3
        view.layer.shadowOpacity = 0.2
        return view
    }()

    private let difficultyLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 11, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()

    private let completionRateLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = .systemGreen
        return label
    }()

    private let progressView: UIProgressView = {
        let progress = UIProgressView(progressViewStyle: .default)
        progress.progressTintColor = .systemPurple
        progress.trackTintColor = .systemGray5
        progress.layer.cornerRadius = 2
        progress.clipsToBounds = true
        return progress
    }()
    
    private let actionsCountLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let durationLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let timeIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "clock")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let chevronIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        contentView.addSubview(containerView)
        containerView.layer.insertSublayer(gradientLayer, at: 0)

        difficultyView.addSubview(difficultyLabel)

        [accentBar, routeNumberLabel, routeNameLabel, locationIcon, locationLabel,
         difficultyView, completionRateLabel, progressView, actionsCountLabel,
         timeIcon, durationLabel, chevronIcon].forEach {
            containerView.addSubview($0)
        }

        setupConstraints()
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        gradientLayer.frame = containerView.bounds
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }

        accentBar.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
            make.width.equalTo(4)
        }

        routeNumberLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(12)
            make.leading.equalTo(accentBar.snp.trailing).offset(12)
            make.width.equalTo(50)
            make.height.equalTo(24)
        }

        chevronIcon.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().inset(16)
            make.width.height.equalTo(18)
        }

        difficultyView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(12)
            make.trailing.equalTo(chevronIcon.snp.leading).offset(-12)
            make.width.equalTo(70)
            make.height.equalTo(24)
        }

        difficultyLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(4)
        }

        routeNameLabel.snp.makeConstraints { make in
            make.top.equalTo(routeNumberLabel.snp.bottom).offset(6)
            make.leading.equalTo(accentBar.snp.trailing).offset(12)
            make.trailing.equalTo(difficultyView.snp.leading).offset(-12)
        }

        locationIcon.snp.makeConstraints { make in
            make.top.equalTo(routeNameLabel.snp.bottom).offset(8)
            make.leading.equalTo(accentBar.snp.trailing).offset(12)
            make.width.height.equalTo(16)
        }

        locationLabel.snp.makeConstraints { make in
            make.centerY.equalTo(locationIcon)
            make.leading.equalTo(locationIcon.snp.trailing).offset(6)
            make.trailing.equalTo(completionRateLabel.snp.leading).offset(-12)
        }

        completionRateLabel.snp.makeConstraints { make in
            make.centerY.equalTo(locationIcon)
            make.trailing.equalTo(chevronIcon.snp.leading).offset(-12)
        }

        progressView.snp.makeConstraints { make in
            make.top.equalTo(locationIcon.snp.bottom).offset(8)
            make.leading.equalTo(accentBar.snp.trailing).offset(12)
            make.trailing.equalTo(chevronIcon.snp.leading).offset(-12)
            make.height.equalTo(4)
        }

        timeIcon.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(12)
            make.leading.equalTo(accentBar.snp.trailing).offset(12)
            make.width.height.equalTo(16)
        }

        durationLabel.snp.makeConstraints { make in
            make.centerY.equalTo(timeIcon)
            make.leading.equalTo(timeIcon.snp.trailing).offset(6)
        }

        actionsCountLabel.snp.makeConstraints { make in
            make.centerY.equalTo(timeIcon)
            make.trailing.equalTo(chevronIcon.snp.leading).offset(-12)
        }
    }
    
    // MARK: - Configuration
    func configure(with route: Route) {
        routeNumberLabel.text = route.routeNumber
        routeNameLabel.text = route.name
        locationLabel.text = route.location

        // Difficulty
        difficultyView.backgroundColor = route.difficulty.color
        difficultyLabel.text = route.difficulty.displayName

        // Completion rate
        let completionPercentage = Int(route.completionRate * 100)
        completionRateLabel.text = "\(completionPercentage)%"

        // Progress view
        progressView.progress = Float(route.completionRate)

        // Update progress color based on completion rate
        if route.completionRate >= 0.8 {
            progressView.progressTintColor = .systemGreen
            completionRateLabel.textColor = .systemGreen
        } else if route.completionRate >= 0.5 {
            progressView.progressTintColor = .systemOrange
            completionRateLabel.textColor = .systemOrange
        } else {
            progressView.progressTintColor = .systemRed
            completionRateLabel.textColor = .systemRed
        }

        // Actions count
        actionsCountLabel.text = "\(route.successfulActions)/\(route.totalActions) actions"

        // Duration
        durationLabel.text = route.formattedDuration

        // Accent bar color based on difficulty
        accentBar.backgroundColor = route.difficulty.color

        // Add subtle animation
        addHoverEffect()
    }

    private func addHoverEffect() {
        let animation = CABasicAnimation(keyPath: "shadowOpacity")
        animation.fromValue = 0.15
        animation.toValue = 0.25
        animation.duration = 0.3
        animation.autoreverses = true
        animation.repeatCount = .infinity
        containerView.layer.add(animation, forKey: "shadowPulse")
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        routeNumberLabel.text = nil
        routeNameLabel.text = nil
        locationLabel.text = nil
        difficultyLabel.text = nil
        completionRateLabel.text = nil
        actionsCountLabel.text = nil
        durationLabel.text = nil
        progressView.progress = 0
        containerView.layer.removeAllAnimations()
    }
}
