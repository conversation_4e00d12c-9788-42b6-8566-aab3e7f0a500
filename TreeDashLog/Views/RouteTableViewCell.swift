//
//  RouteTableViewCell.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class RouteTableViewCell: UITableViewCell {
    static let identifier = "RouteTableViewCell"
    
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        view.layer.shadowOpacity = 0.1
        return view
    }()
    
    private let routeNumberLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .semibold)
        label.textColor = .systemPurple
        return label
    }()
    
    private let routeNameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .label
        label.numberOfLines = 1
        return label
    }()
    
    private let locationLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryLabel
        label.numberOfLines = 1
        return label
    }()
    
    private let locationIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "location")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let difficultyView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 8
        return view
    }()
    
    private let difficultyLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private let completionRateLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .semibold)
        label.textColor = .systemGreen
        return label
    }()
    
    private let actionsCountLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let durationLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let timeIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "clock")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let chevronIcon: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        
        difficultyView.addSubview(difficultyLabel)
        
        [routeNumberLabel, routeNameLabel, locationIcon, locationLabel, 
         difficultyView, completionRateLabel, actionsCountLabel, 
         timeIcon, durationLabel, chevronIcon].forEach {
            containerView.addSubview($0)
        }
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        
        routeNumberLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().inset(12)
        }
        
        chevronIcon.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.trailing.equalToSuperview().inset(12)
            make.width.height.equalTo(16)
        }
        
        routeNameLabel.snp.makeConstraints { make in
            make.top.equalTo(routeNumberLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().inset(12)
            make.trailing.equalTo(chevronIcon.snp.leading).offset(-8)
        }
        
        locationIcon.snp.makeConstraints { make in
            make.top.equalTo(routeNameLabel.snp.bottom).offset(8)
            make.leading.equalToSuperview().inset(12)
            make.width.height.equalTo(14)
        }
        
        locationLabel.snp.makeConstraints { make in
            make.centerY.equalTo(locationIcon)
            make.leading.equalTo(locationIcon.snp.trailing).offset(4)
            make.trailing.equalTo(difficultyView.snp.leading).offset(-8)
        }
        
        difficultyView.snp.makeConstraints { make in
            make.top.equalTo(routeNumberLabel)
            make.trailing.equalTo(chevronIcon.snp.leading).offset(-8)
            make.width.equalTo(60)
            make.height.equalTo(20)
        }
        
        difficultyLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(2)
        }
        
        completionRateLabel.snp.makeConstraints { make in
            make.top.equalTo(difficultyView.snp.bottom).offset(4)
            make.trailing.equalTo(chevronIcon.snp.leading).offset(-8)
        }
        
        timeIcon.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(12)
            make.leading.equalToSuperview().inset(12)
            make.width.height.equalTo(14)
        }
        
        durationLabel.snp.makeConstraints { make in
            make.centerY.equalTo(timeIcon)
            make.leading.equalTo(timeIcon.snp.trailing).offset(4)
        }
        
        actionsCountLabel.snp.makeConstraints { make in
            make.centerY.equalTo(timeIcon)
            make.trailing.equalTo(chevronIcon.snp.leading).offset(-8)
        }
    }
    
    // MARK: - Configuration
    func configure(with route: Route) {
        routeNumberLabel.text = route.routeNumber
        routeNameLabel.text = route.name
        locationLabel.text = route.location
        
        // Difficulty
        difficultyView.backgroundColor = route.difficulty.color
        difficultyLabel.text = route.difficulty.displayName
        
        // Completion rate
        let completionPercentage = Int(route.completionRate * 100)
        completionRateLabel.text = "\(completionPercentage)%"
        
        // Actions count
        actionsCountLabel.text = "\(route.successfulActions)/\(route.totalActions) actions"
        
        // Duration
        durationLabel.text = route.formattedDuration
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        routeNumberLabel.text = nil
        routeNameLabel.text = nil
        locationLabel.text = nil
        difficultyLabel.text = nil
        completionRateLabel.text = nil
        actionsCountLabel.text = nil
        durationLabel.text = nil
    }
}
