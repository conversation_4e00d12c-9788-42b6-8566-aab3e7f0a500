//
//  ActionPerformanceTableViewCell.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class ActionPerformanceTableViewCell: UITableViewCell {
    static let identifier = "ActionPerformanceTableViewCell"
    
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.systemGray5.cgColor
        return view
    }()
    
    private let actionIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemPurple
        imageView.backgroundColor = .systemPurple.withAlphaComponent(0.1)
        imageView.layer.cornerRadius = 20
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    private let actionNameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = .label
        return label
    }()
    
    private let successRateLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .semibold)
        label.textColor = .systemGreen
        return label
    }()
    
    private let averageTimeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let efficiencyRatingView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 8
        return view
    }()
    
    private let efficiencyRatingLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 10, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private let progressBar: UIProgressView = {
        let progress = UIProgressView(progressViewStyle: .default)
        progress.trackTintColor = .systemGray5
        progress.layer.cornerRadius = 2
        progress.clipsToBounds = true
        return progress
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        efficiencyRatingView.addSubview(efficiencyRatingLabel)
        
        [actionIconImageView, actionNameLabel, successRateLabel, averageTimeLabel, 
         efficiencyRatingView, progressBar].forEach {
            containerView.addSubview($0)
        }
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 0, bottom: 4, right: 0))
        }
        
        actionIconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }
        
        actionNameLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().inset(12)
            make.trailing.equalTo(efficiencyRatingView.snp.leading).offset(-8)
        }
        
        successRateLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalTo(actionNameLabel.snp.bottom).offset(2)
        }
        
        averageTimeLabel.snp.makeConstraints { make in
            make.leading.equalTo(successRateLabel.snp.trailing).offset(12)
            make.centerY.equalTo(successRateLabel)
        }
        
        progressBar.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalTo(successRateLabel.snp.bottom).offset(6)
            make.trailing.equalTo(efficiencyRatingView.snp.leading).offset(-8)
            make.height.equalTo(4)
            make.bottom.equalToSuperview().inset(12)
        }
        
        efficiencyRatingView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(24)
        }
        
        efficiencyRatingLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(4)
        }
    }
    
    // MARK: - Configuration
    func configure(with performance: ActionPerformance) {
        actionIconImageView.image = UIImage(systemName: performance.actionType.systemIcon)
        actionNameLabel.text = performance.actionName
        
        let successPercentage = Int(performance.successRate * 100)
        successRateLabel.text = "\(successPercentage)% success"
        
        let minutes = Int(performance.averageTime) / 60
        let seconds = Int(performance.averageTime) % 60
        averageTimeLabel.text = String(format: "avg %02d:%02d", minutes, seconds)
        
        // Progress bar
        progressBar.progress = Float(performance.successRate)
        progressBar.progressTintColor = performance.efficiencyRating.color
        
        // Efficiency rating
        efficiencyRatingView.backgroundColor = performance.efficiencyRating.color
        efficiencyRatingLabel.text = performance.efficiencyRating.displayName
        
        // Update success rate color based on performance
        if performance.successRate >= 0.8 {
            successRateLabel.textColor = .systemGreen
        } else if performance.successRate >= 0.5 {
            successRateLabel.textColor = .systemOrange
        } else {
            successRateLabel.textColor = .systemRed
        }
        
        // Add subtle animation
        addPerformanceAnimation(performance.successRate)
    }
    
    private func addPerformanceAnimation(_ successRate: Double) {
        if successRate >= 0.8 {
            // Excellent performance - subtle glow
            let animation = CABasicAnimation(keyPath: "shadowOpacity")
            animation.fromValue = 0.0
            animation.toValue = 0.3
            animation.duration = 1.0
            animation.autoreverses = true
            animation.repeatCount = .infinity
            containerView.layer.shadowColor = UIColor.systemGreen.cgColor
            containerView.layer.shadowRadius = 4
            containerView.layer.add(animation, forKey: "excellentGlow")
        } else if successRate < 0.3 {
            // Poor performance - warning pulse
            containerView.layer.borderColor = UIColor.systemRed.withAlphaComponent(0.3).cgColor
        }
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        actionIconImageView.image = nil
        actionNameLabel.text = nil
        successRateLabel.text = nil
        averageTimeLabel.text = nil
        efficiencyRatingLabel.text = nil
        progressBar.progress = 0
        containerView.layer.removeAllAnimations()
        containerView.layer.borderColor = UIColor.systemGray5.cgColor
    }
}
