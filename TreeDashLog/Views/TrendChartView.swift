//
//  TrendChartView.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit

class TrendChartView: UIView {
    
    // MARK: - Properties
    private var trendData: SuccessRateTrend?
    private let lineLayer = CAShapeLayer()
    private let gradientLayer = CAGradientLayer()
    private let gridLayer = CAShapeLayer()
    private var pointLayers: [CAShapeLayer] = []
    private var labelViews: [UILabel] = []
    
    // MARK: - Configuration
    private let lineWidth: CGFloat = 3.0
    private let pointRadius: CGFloat = 6.0
    private let gridLineWidth: CGFloat = 0.5
    private let padding: CGFloat = 40.0
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupLayers()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupLayers()
    }
    
    // MARK: - Setup
    private func setupLayers() {
        // Grid layer
        gridLayer.strokeColor = UIColor.systemGray5.cgColor
        gridLayer.lineWidth = gridLineWidth
        gridLayer.fillColor = UIColor.clear.cgColor
        layer.addSublayer(gridLayer)
        
        // Gradient layer
        gradientLayer.colors = [
            UIColor.systemPurple.withAlphaComponent(0.3).cgColor,
            UIColor.systemPurple.withAlphaComponent(0.1).cgColor,
            UIColor.clear.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        layer.addSublayer(gradientLayer)
        
        // Line layer
        lineLayer.strokeColor = UIColor.systemPurple.cgColor
        lineLayer.lineWidth = lineWidth
        lineLayer.fillColor = UIColor.clear.cgColor
        lineLayer.lineCap = .round
        lineLayer.lineJoin = .round
        layer.addSublayer(lineLayer)
    }
    
    // MARK: - Configuration
    func configure(with trend: SuccessRateTrend) {
        self.trendData = trend
        setNeedsDisplay()
    }
    
    // MARK: - Drawing
    override func layoutSubviews() {
        super.layoutSubviews()
        drawChart()
    }
    
    private func drawChart() {
        guard let trend = trendData, !trend.dataPoints.isEmpty else {
            showEmptyState()
            return
        }
        
        let chartRect = CGRect(
            x: padding,
            y: padding / 2,
            width: bounds.width - padding * 2,
            height: bounds.height - padding
        )
        
        drawGrid(in: chartRect)
        drawLine(in: chartRect, dataPoints: trend.dataPoints)
        drawGradient(in: chartRect, dataPoints: trend.dataPoints)
        drawPoints(in: chartRect, dataPoints: trend.dataPoints)
        drawLabels(in: chartRect, dataPoints: trend.dataPoints)
    }
    
    private func showEmptyState() {
        clearChart()
        
        let emptyLabel = UILabel()
        emptyLabel.text = "No trend data available"
        emptyLabel.font = .systemFont(ofSize: 16, weight: .medium)
        emptyLabel.textColor = .secondaryLabel
        emptyLabel.textAlignment = .center
        
        addSubview(emptyLabel)
        emptyLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        labelViews.append(emptyLabel)
    }
    
    private func clearChart() {
        pointLayers.forEach { $0.removeFromSuperlayer() }
        pointLayers.removeAll()
        
        labelViews.forEach { $0.removeFromSuperview() }
        labelViews.removeAll()
        
        lineLayer.path = nil
        gradientLayer.mask = nil
        gridLayer.path = nil
    }
    
    private func drawGrid(in rect: CGRect) {
        let gridPath = UIBezierPath()
        
        // Horizontal grid lines (success rate)
        for i in 0...5 {
            let y = rect.minY + (rect.height / 5) * CGFloat(i)
            gridPath.move(to: CGPoint(x: rect.minX, y: y))
            gridPath.addLine(to: CGPoint(x: rect.maxX, y: y))
        }
        
        // Vertical grid lines (time periods)
        let dataPointCount = trendData?.dataPoints.count ?? 1
        let verticalLines = min(dataPointCount, 6)
        
        guard verticalLines > 1 else { return }
        
        for i in 0..<verticalLines {
            let x = rect.minX + (rect.width / CGFloat(verticalLines - 1)) * CGFloat(i)
            gridPath.move(to: CGPoint(x: x, y: rect.minY))
            gridPath.addLine(to: CGPoint(x: x, y: rect.maxY))
        }
        
        gridLayer.path = gridPath.cgPath
    }
    
    private func drawLine(in rect: CGRect, dataPoints: [TrendDataPoint]) {
        guard dataPoints.count > 1 else { return }
        
        let linePath = UIBezierPath()
        
        for (index, point) in dataPoints.enumerated() {
            let x = rect.minX + (rect.width * CGFloat(index)) / CGFloat(dataPoints.count - 1)
            let y = rect.maxY - (rect.height * CGFloat(point.successRate))
            
            if index == 0 {
                linePath.move(to: CGPoint(x: x, y: y))
            } else {
                linePath.addLine(to: CGPoint(x: x, y: y))
            }
        }
        
        lineLayer.path = linePath.cgPath
        
        // Animate line drawing
        let animation = CABasicAnimation(keyPath: "strokeEnd")
        animation.fromValue = 0
        animation.toValue = 1
        animation.duration = 1.5
        animation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        lineLayer.add(animation, forKey: "lineAnimation")
    }
    
    private func drawGradient(in rect: CGRect, dataPoints: [TrendDataPoint]) {
        guard !dataPoints.isEmpty else { return }
        
        let gradientPath = UIBezierPath()
        
        // Start from bottom left
        gradientPath.move(to: CGPoint(x: rect.minX, y: rect.maxY))
        
        // Draw line following data points
        for (index, point) in dataPoints.enumerated() {
            let x = rect.minX + (rect.width * CGFloat(index)) / CGFloat(max(dataPoints.count - 1, 1))
            let y = rect.maxY - (rect.height * CGFloat(point.successRate))
            gradientPath.addLine(to: CGPoint(x: x, y: y))
        }
        
        // Close path to bottom right
        let lastX = rect.minX + rect.width
        gradientPath.addLine(to: CGPoint(x: lastX, y: rect.maxY))
        gradientPath.close()
        
        gradientLayer.frame = bounds
        
        let maskLayer = CAShapeLayer()
        maskLayer.path = gradientPath.cgPath
        gradientLayer.mask = maskLayer
    }
    
    private func drawPoints(in rect: CGRect, dataPoints: [TrendDataPoint]) {
        // Remove existing point layers
        pointLayers.forEach { $0.removeFromSuperlayer() }
        pointLayers.removeAll()
        
        for (index, point) in dataPoints.enumerated() {
            let x = rect.minX + (rect.width * CGFloat(index)) / CGFloat(max(dataPoints.count - 1, 1))
            let y = rect.maxY - (rect.height * CGFloat(point.successRate))
            
            let pointLayer = CAShapeLayer()
            let pointPath = UIBezierPath(arcCenter: CGPoint(x: x, y: y), radius: pointRadius, startAngle: 0, endAngle: .pi * 2, clockwise: true)
            
            pointLayer.path = pointPath.cgPath
            pointLayer.fillColor = UIColor.systemPurple.cgColor
            pointLayer.strokeColor = UIColor.white.cgColor
            pointLayer.lineWidth = 2
            
            // Add shadow
            pointLayer.shadowColor = UIColor.black.cgColor
            pointLayer.shadowOffset = CGSize(width: 0, height: 2)
            pointLayer.shadowRadius = 3
            pointLayer.shadowOpacity = 0.3
            
            layer.addSublayer(pointLayer)
            pointLayers.append(pointLayer)
            
            // Animate point appearance
            let scaleAnimation = CABasicAnimation(keyPath: "transform.scale")
            scaleAnimation.fromValue = 0
            scaleAnimation.toValue = 1
            scaleAnimation.duration = 0.5
            scaleAnimation.beginTime = CACurrentMediaTime() + Double(index) * 0.1
            scaleAnimation.timingFunction = CAMediaTimingFunction(name: .easeOut)
            scaleAnimation.fillMode = .backwards
            pointLayer.add(scaleAnimation, forKey: "pointAnimation")
        }
    }
    
    private func drawLabels(in rect: CGRect, dataPoints: [TrendDataPoint]) {
        // Remove existing labels
        labelViews.forEach { $0.removeFromSuperview() }
        labelViews.removeAll()
        
        // Y-axis labels (success rate)
        for i in 0...5 {
            let percentage = i * 20
            let y = rect.maxY - (rect.height / 5) * CGFloat(i)
            
            let label = UILabel()
            label.text = "\(percentage)%"
            label.font = .systemFont(ofSize: 12, weight: .medium)
            label.textColor = .secondaryLabel
            label.textAlignment = .right
            label.frame = CGRect(x: 0, y: y - 10, width: padding - 8, height: 20)
            addSubview(label)
            labelViews.append(label)
        }
        
        // X-axis labels (time periods)
        let labelCount = min(dataPoints.count, 5)
        for i in 0..<labelCount {
            let dataIndex = (dataPoints.count - 1) * i / max(labelCount - 1, 1)
            let point = dataPoints[dataIndex]
            let x = rect.minX + (rect.width * CGFloat(i)) / CGFloat(max(labelCount - 1, 1))
            
            let label = UILabel()
            label.text = point.formattedPeriod
            label.font = .systemFont(ofSize: 10, weight: .medium)
            label.textColor = .secondaryLabel
            label.textAlignment = .center
            label.frame = CGRect(x: x - 30, y: rect.maxY + 8, width: 60, height: 20)
            addSubview(label)
            labelViews.append(label)
        }
        
        // Axis titles
        let yAxisTitle = UILabel()
        yAxisTitle.text = "Success Rate"
        yAxisTitle.font = .systemFont(ofSize: 14, weight: .semibold)
        yAxisTitle.textColor = .label
        yAxisTitle.transform = CGAffineTransform(rotationAngle: -.pi / 2)
        yAxisTitle.frame = CGRect(x: 5, y: bounds.midY - 40, width: 80, height: 20)
        addSubview(yAxisTitle)
        labelViews.append(yAxisTitle)
        
        let xAxisTitle = UILabel()
        xAxisTitle.text = "Time Period"
        xAxisTitle.font = .systemFont(ofSize: 14, weight: .semibold)
        xAxisTitle.textColor = .label
        xAxisTitle.textAlignment = .center
        xAxisTitle.frame = CGRect(x: bounds.midX - 40, y: bounds.maxY - 25, width: 80, height: 20)
        addSubview(xAxisTitle)
        labelViews.append(xAxisTitle)
    }
    
    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first,
              let trend = trendData else { return }
        
        let location = touch.location(in: self)
        let chartRect = CGRect(
            x: padding,
            y: padding / 2,
            width: bounds.width - padding * 2,
            height: bounds.height - padding
        )
        
        // Find closest data point
        var closestIndex = 0
        var closestDistance: CGFloat = .greatestFiniteMagnitude
        
        for (index, point) in trend.dataPoints.enumerated() {
            let x = chartRect.minX + (chartRect.width * CGFloat(index)) / CGFloat(max(trend.dataPoints.count - 1, 1))
            let y = chartRect.maxY - (chartRect.height * CGFloat(point.successRate))
            
            let distance = sqrt(pow(location.x - x, 2) + pow(location.y - y, 2))
            if distance < closestDistance {
                closestDistance = distance
                closestIndex = index
            }
        }
        
        if closestDistance < 50 { // Within 50 points
            showDataPointTooltip(for: trend.dataPoints[closestIndex], at: location)
        }
    }
    
    private func showDataPointTooltip(for dataPoint: TrendDataPoint, at location: CGPoint) {
        let tooltip = UILabel()
        tooltip.text = """
        \(dataPoint.formattedPeriod)
        Success: \(String(format: "%.1f%%", dataPoint.successRate * 100))
        Routes: \(dataPoint.routeCount)
        """
        tooltip.font = .systemFont(ofSize: 12, weight: .semibold)
        tooltip.textColor = .white
        tooltip.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        tooltip.textAlignment = .center
        tooltip.layer.cornerRadius = 8
        tooltip.layer.masksToBounds = true
        tooltip.numberOfLines = 3
        
        // Size tooltip
        tooltip.sizeToFit()
        let padding: CGFloat = 12
        tooltip.frame = CGRect(
            x: 0, y: 0,
            width: tooltip.frame.width + padding * 2,
            height: tooltip.frame.height + padding
        )
        
        // Position tooltip
        var tooltipX = location.x - tooltip.frame.width / 2
        tooltipX = max(0, min(tooltipX, bounds.width - tooltip.frame.width))
        
        tooltip.frame.origin = CGPoint(x: tooltipX, y: location.y - tooltip.frame.height - 10)
        
        addSubview(tooltip)
        
        // Animate tooltip
        tooltip.alpha = 0
        tooltip.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        
        UIView.animate(withDuration: 0.2, animations: {
            tooltip.alpha = 1
            tooltip.transform = .identity
        }) { _ in
            // Remove tooltip after delay
            UIView.animate(withDuration: 0.2, delay: 2.0, options: [], animations: {
                tooltip.alpha = 0
            }) { _ in
                tooltip.removeFromSuperview()
            }
        }
    }
}
