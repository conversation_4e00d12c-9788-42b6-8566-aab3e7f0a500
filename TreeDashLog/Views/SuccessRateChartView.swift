//
//  SuccessRateChartView.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit

class SuccessRateChartView: UIView {
    
    // MARK: - Properties
    private var dataPoints: [SuccessRateDataPoint] = []
    private let lineLayer = CAShapeLayer()
    private let gradientLayer = CAGradientLayer()
    private let gridLayer = CAShapeLayer()
    private var pointLayers: [CAShapeLayer] = []
    
    // MARK: - Configuration
    private let lineWidth: CGFloat = 3.0
    private let pointRadius: CGFloat = 6.0
    private let gridLineWidth: CGFloat = 0.5
    private let padding: CGFloat = 40.0
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupLayers()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupLayers()
    }
    
    // MARK: - Setup
    private func setupLayers() {
        // Grid layer
        gridLayer.strokeColor = UIColor.systemGray5.cgColor
        gridLayer.lineWidth = gridLineWidth
        gridLayer.fillColor = UIColor.clear.cgColor
        layer.addSublayer(gridLayer)
        
        // Gradient layer
        gradientLayer.colors = [
            UIColor.systemPurple.withAlphaComponent(0.3).cgColor,
            UIColor.systemPurple.withAlphaComponent(0.1).cgColor,
            UIColor.clear.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        layer.addSublayer(gradientLayer)
        
        // Line layer
        lineLayer.strokeColor = UIColor.systemPurple.cgColor
        lineLayer.lineWidth = lineWidth
        lineLayer.fillColor = UIColor.clear.cgColor
        lineLayer.lineCap = .round
        lineLayer.lineJoin = .round
        layer.addSublayer(lineLayer)
    }
    
    // MARK: - Configuration
    func configure(with dataPoints: [SuccessRateDataPoint]) {
        self.dataPoints = dataPoints
        setNeedsDisplay()
    }
    
    // MARK: - Drawing
    override func layoutSubviews() {
        super.layoutSubviews()
        drawChart()
    }
    
    private func drawChart() {
        guard !dataPoints.isEmpty else { return }
        
        let chartRect = CGRect(
            x: padding,
            y: padding / 2,
            width: bounds.width - padding * 2,
            height: bounds.height - padding
        )
        
        drawGrid(in: chartRect)
        drawLine(in: chartRect)
        drawGradient(in: chartRect)
        drawPoints(in: chartRect)
        drawLabels(in: chartRect)
    }
    
    private func drawGrid(in rect: CGRect) {
        let gridPath = UIBezierPath()
        
        // Horizontal grid lines (success rate)
        for i in 0...5 {
            let y = rect.minY + (rect.height / 5) * CGFloat(i)
            gridPath.move(to: CGPoint(x: rect.minX, y: y))
            gridPath.addLine(to: CGPoint(x: rect.maxX, y: y))
        }
        
        // Vertical grid lines (attempts)
        let maxAttempts = dataPoints.map { $0.attemptNumber }.max() ?? 1
        let verticalLines = min(maxAttempts, 10)
        
        for i in 0...verticalLines {
            let x = rect.minX + (rect.width / CGFloat(verticalLines)) * CGFloat(i)
            gridPath.move(to: CGPoint(x: x, y: rect.minY))
            gridPath.addLine(to: CGPoint(x: x, y: rect.maxY))
        }
        
        gridLayer.path = gridPath.cgPath
    }
    
    private func drawLine(in rect: CGRect) {
        guard dataPoints.count > 1 else { return }
        
        let linePath = UIBezierPath()
        let maxAttempts = dataPoints.map { $0.attemptNumber }.max() ?? 1
        
        for (index, point) in dataPoints.enumerated() {
            let x = rect.minX + (rect.width * CGFloat(point.attemptNumber - 1)) / CGFloat(maxAttempts - 1)
            let y = rect.maxY - (rect.height * CGFloat(point.successRate))
            
            if index == 0 {
                linePath.move(to: CGPoint(x: x, y: y))
            } else {
                linePath.addLine(to: CGPoint(x: x, y: y))
            }
        }
        
        lineLayer.path = linePath.cgPath
        
        // Animate line drawing
        let animation = CABasicAnimation(keyPath: "strokeEnd")
        animation.fromValue = 0
        animation.toValue = 1
        animation.duration = 1.5
        animation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        lineLayer.add(animation, forKey: "lineAnimation")
    }
    
    private func drawGradient(in rect: CGRect) {
        guard dataPoints.count > 1 else { return }
        
        let gradientPath = UIBezierPath()
        let maxAttempts = dataPoints.map { $0.attemptNumber }.max() ?? 1
        
        // Start from bottom left
        gradientPath.move(to: CGPoint(x: rect.minX, y: rect.maxY))
        
        // Draw line following data points
        for point in dataPoints {
            let x = rect.minX + (rect.width * CGFloat(point.attemptNumber - 1)) / CGFloat(maxAttempts - 1)
            let y = rect.maxY - (rect.height * CGFloat(point.successRate))
            gradientPath.addLine(to: CGPoint(x: x, y: y))
        }
        
        // Close path to bottom right
        let lastX = rect.minX + rect.width
        gradientPath.addLine(to: CGPoint(x: lastX, y: rect.maxY))
        gradientPath.close()
        
        gradientLayer.frame = bounds
        
        let maskLayer = CAShapeLayer()
        maskLayer.path = gradientPath.cgPath
        gradientLayer.mask = maskLayer
    }
    
    private func drawPoints(in rect: CGRect) {
        // Remove existing point layers
        pointLayers.forEach { $0.removeFromSuperlayer() }
        pointLayers.removeAll()
        
        let maxAttempts = dataPoints.map { $0.attemptNumber }.max() ?? 1
        
        for (index, point) in dataPoints.enumerated() {
            let x = rect.minX + (rect.width * CGFloat(point.attemptNumber - 1)) / CGFloat(maxAttempts - 1)
            let y = rect.maxY - (rect.height * CGFloat(point.successRate))
            
            let pointLayer = CAShapeLayer()
            let pointPath = UIBezierPath(arcCenter: CGPoint(x: x, y: y), radius: pointRadius, startAngle: 0, endAngle: .pi * 2, clockwise: true)
            
            pointLayer.path = pointPath.cgPath
            pointLayer.fillColor = UIColor.systemPurple.cgColor
            pointLayer.strokeColor = UIColor.white.cgColor
            pointLayer.lineWidth = 2
            
            // Add shadow
            pointLayer.shadowColor = UIColor.black.cgColor
            pointLayer.shadowOffset = CGSize(width: 0, height: 2)
            pointLayer.shadowRadius = 3
            pointLayer.shadowOpacity = 0.3
            
            layer.addSublayer(pointLayer)
            pointLayers.append(pointLayer)
            
            // Animate point appearance
            let scaleAnimation = CABasicAnimation(keyPath: "transform.scale")
            scaleAnimation.fromValue = 0
            scaleAnimation.toValue = 1
            scaleAnimation.duration = 0.5
            scaleAnimation.beginTime = CACurrentMediaTime() + Double(index) * 0.1
            scaleAnimation.timingFunction = CAMediaTimingFunction(name: .easeOut)
            scaleAnimation.fillMode = .backwards
            pointLayer.add(scaleAnimation, forKey: "pointAnimation")
        }
    }
    
    private func drawLabels(in rect: CGRect) {
        // Remove existing labels
        subviews.forEach { $0.removeFromSuperview() }
        
        // Y-axis labels (success rate)
        for i in 0...5 {
            let percentage = i * 20
            let y = rect.maxY - (rect.height / 5) * CGFloat(i)
            
            let label = UILabel()
            label.text = "\(percentage)%"
            label.font = .systemFont(ofSize: 12, weight: .medium)
            label.textColor = .secondaryLabel
            label.textAlignment = .right
            label.frame = CGRect(x: 0, y: y - 10, width: padding - 8, height: 20)
            addSubview(label)
        }
        
        // X-axis labels (attempts)
        let maxAttempts = dataPoints.map { $0.attemptNumber }.max() ?? 1
        let labelCount = min(maxAttempts, 6)
        
        for i in 0..<labelCount {
            let attemptNumber = (maxAttempts * i) / (labelCount - 1) + 1
            let x = rect.minX + (rect.width * CGFloat(i)) / CGFloat(labelCount - 1)
            
            let label = UILabel()
            label.text = "\(attemptNumber)"
            label.font = .systemFont(ofSize: 12, weight: .medium)
            label.textColor = .secondaryLabel
            label.textAlignment = .center
            label.frame = CGRect(x: x - 20, y: rect.maxY + 8, width: 40, height: 20)
            addSubview(label)
        }
        
        // Axis titles
        let yAxisTitle = UILabel()
        yAxisTitle.text = "Success Rate"
        yAxisTitle.font = .systemFont(ofSize: 14, weight: .semibold)
        yAxisTitle.textColor = .label
        yAxisTitle.transform = CGAffineTransform(rotationAngle: -.pi / 2)
        yAxisTitle.frame = CGRect(x: 5, y: bounds.midY - 40, width: 80, height: 20)
        addSubview(yAxisTitle)
        
        let xAxisTitle = UILabel()
        xAxisTitle.text = "Attempt Number"
        xAxisTitle.font = .systemFont(ofSize: 14, weight: .semibold)
        xAxisTitle.textColor = .label
        xAxisTitle.textAlignment = .center
        xAxisTitle.frame = CGRect(x: bounds.midX - 60, y: bounds.maxY - 25, width: 120, height: 20)
        addSubview(xAxisTitle)
    }
}
