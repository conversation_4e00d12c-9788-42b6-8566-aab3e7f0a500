//
//  ImprovementSuggestionTableViewCell.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class ImprovementSuggestionTableViewCell: UITableViewCell {
    static let identifier = "ImprovementSuggestionTableViewCell"
    
    // MARK: - UI Components
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.systemGray5.cgColor
        return view
    }()
    
    private let priorityIndicator: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 3
        return view
    }()
    
    private let categoryIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemPurple
        imageView.backgroundColor = .systemPurple.withAlphaComponent(0.1)
        imageView.layer.cornerRadius = 16
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = .label
        label.numberOfLines = 2
        return label
    }()
    
    private let descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryLabel
        label.numberOfLines = 2
        return label
    }()
    
    private let expectedImprovementLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .semibold)
        label.textColor = .systemGreen
        label.numberOfLines = 1
        return label
    }()
    
    private let priorityLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 10, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private let difficultyView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 8
        return view
    }()
    
    private let difficultyLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 10, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private let chevronImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        difficultyView.addSubview(difficultyLabel)
        
        [priorityIndicator, categoryIconImageView, titleLabel, descriptionLabel, 
         expectedImprovementLabel, priorityLabel, difficultyView, chevronImageView].forEach {
            containerView.addSubview($0)
        }
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 0, bottom: 4, right: 0))
        }
        
        priorityIndicator.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
            make.width.equalTo(4)
        }
        
        categoryIconImageView.snp.makeConstraints { make in
            make.leading.equalTo(priorityIndicator.snp.trailing).offset(12)
            make.top.equalToSuperview().inset(12)
            make.width.height.equalTo(32)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
        
        difficultyView.snp.makeConstraints { make in
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
            make.top.equalToSuperview().inset(12)
            make.width.equalTo(50)
            make.height.equalTo(20)
        }
        
        difficultyLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(2)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(categoryIconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().inset(12)
            make.trailing.equalTo(difficultyView.snp.leading).offset(-8)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.leading.equalTo(categoryIconImageView.snp.trailing).offset(12)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
        }
        
        expectedImprovementLabel.snp.makeConstraints { make in
            make.leading.equalTo(categoryIconImageView.snp.trailing).offset(12)
            make.top.equalTo(descriptionLabel.snp.bottom).offset(4)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
            make.bottom.equalToSuperview().inset(12)
        }
        
        priorityLabel.snp.makeConstraints { make in
            make.centerX.equalTo(categoryIconImageView)
            make.top.equalTo(categoryIconImageView.snp.bottom).offset(4)
            make.width.equalTo(40)
            make.height.equalTo(16)
        }
    }
    
    // MARK: - Configuration
    func configure(with suggestion: ImprovementSuggestion) {
        categoryIconImageView.image = UIImage(systemName: suggestion.category.systemIcon)
        titleLabel.text = suggestion.title
        descriptionLabel.text = suggestion.description
        expectedImprovementLabel.text = "Expected: \(suggestion.expectedImprovement)"
        
        // Priority indicator and label
        priorityIndicator.backgroundColor = suggestion.priority.color
        priorityLabel.text = suggestion.priority.displayName.prefix(4).uppercased()
        priorityLabel.backgroundColor = suggestion.priority.color
        priorityLabel.layer.cornerRadius = 8
        priorityLabel.layer.masksToBounds = true
        
        // Difficulty
        difficultyView.backgroundColor = suggestion.difficulty.color
        difficultyLabel.text = suggestion.difficulty.displayName
        
        // Update container styling based on priority
        switch suggestion.priority {
        case .high:
            containerView.layer.borderColor = UIColor.systemRed.withAlphaComponent(0.3).cgColor
            containerView.layer.borderWidth = 2
            addHighPriorityAnimation()
        case .medium:
            containerView.layer.borderColor = UIColor.systemOrange.withAlphaComponent(0.3).cgColor
            containerView.layer.borderWidth = 1.5
        case .low:
            containerView.layer.borderColor = UIColor.systemGray5.cgColor
            containerView.layer.borderWidth = 1
        }
        
        // Category-specific styling
        categoryIconImageView.tintColor = suggestion.category == .mental ? .systemIndigo : .systemPurple
    }
    
    private func addHighPriorityAnimation() {
        let pulseAnimation = CABasicAnimation(keyPath: "transform.scale")
        pulseAnimation.fromValue = 1.0
        pulseAnimation.toValue = 1.02
        pulseAnimation.duration = 1.0
        pulseAnimation.autoreverses = true
        pulseAnimation.repeatCount = .infinity
        containerView.layer.add(pulseAnimation, forKey: "highPriorityPulse")
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        categoryIconImageView.image = nil
        titleLabel.text = nil
        descriptionLabel.text = nil
        expectedImprovementLabel.text = nil
        priorityLabel.text = nil
        difficultyLabel.text = nil
        containerView.layer.removeAllAnimations()
        containerView.layer.borderColor = UIColor.systemGray5.cgColor
        containerView.layer.borderWidth = 1
        categoryIconImageView.tintColor = .systemPurple
    }
}
