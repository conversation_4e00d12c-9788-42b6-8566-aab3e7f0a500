//
//  DifficultyDistributionChartView.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit

class DifficultyDistributionChartView: UIView {
    
    // MARK: - Properties
    private var difficultyData: [RouteDifficulty: Int] = [:]
    private var barLayers: [CAShapeLayer] = []
    private var labelViews: [UILabel] = []
    
    // MARK: - Configuration
    private let barSpacing: CGFloat = 8.0
    private let labelHeight: CGFloat = 40.0
    private let animationDuration: TimeInterval = 1.0
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    // MARK: - Setup
    private func setupView() {
        backgroundColor = .clear
    }
    
    // MARK: - Configuration
    func configure(with data: [RouteDifficulty: Int]) {
        self.difficultyData = data
        setNeedsDisplay()
    }
    
    // MARK: - Drawing
    override func layoutSubviews() {
        super.layoutSubviews()
        drawChart()
    }
    
    private func drawChart() {
        // Clear existing content
        clearChart()
        
        guard !difficultyData.isEmpty else {
            showEmptyState()
            return
        }
        
        let chartRect = CGRect(
            x: 0,
            y: 0,
            width: bounds.width,
            height: bounds.height - labelHeight
        )
        
        drawBars(in: chartRect)
        drawLabels()
    }
    
    private func clearChart() {
        barLayers.forEach { $0.removeFromSuperlayer() }
        barLayers.removeAll()
        
        labelViews.forEach { $0.removeFromSuperview() }
        labelViews.removeAll()
    }
    
    private func showEmptyState() {
        let emptyLabel = UILabel()
        emptyLabel.text = "No difficulty data available"
        emptyLabel.font = .systemFont(ofSize: 16, weight: .medium)
        emptyLabel.textColor = .secondaryLabel
        emptyLabel.textAlignment = .center
        
        addSubview(emptyLabel)
        emptyLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        labelViews.append(emptyLabel)
    }
    
    private func drawBars(in rect: CGRect) {
        let difficulties = RouteDifficulty.allCases
        let maxValue = difficultyData.values.max() ?? 1
        let barWidth = (rect.width - CGFloat(difficulties.count - 1) * barSpacing) / CGFloat(difficulties.count)
        
        for (index, difficulty) in difficulties.enumerated() {
            let value = difficultyData[difficulty] ?? 0
            let barHeight = rect.height * CGFloat(value) / CGFloat(maxValue)
            
            let x = CGFloat(index) * (barWidth + barSpacing)
            let y = rect.height - barHeight
            
            let barRect = CGRect(x: x, y: y, width: barWidth, height: barHeight)
            let barLayer = createBarLayer(rect: barRect, color: difficulty.color)
            
            layer.addSublayer(barLayer)
            barLayers.append(barLayer)
            
            // Add value label on top of bar
            if value > 0 {
                let valueLabel = UILabel()
                valueLabel.text = "\(value)"
                valueLabel.font = .systemFont(ofSize: 12, weight: .bold)
                valueLabel.textColor = .label
                valueLabel.textAlignment = .center
                valueLabel.frame = CGRect(x: x, y: y - 20, width: barWidth, height: 16)
                
                addSubview(valueLabel)
                labelViews.append(valueLabel)
            }
        }
        
        // Animate bars
        animateBars()
    }
    
    private func createBarLayer(rect: CGRect, color: UIColor) -> CAShapeLayer {
        let barLayer = CAShapeLayer()
        let barPath = UIBezierPath(roundedRect: rect, cornerRadius: 4)
        
        barLayer.path = barPath.cgPath
        barLayer.fillColor = color.cgColor
        barLayer.strokeColor = UIColor.clear.cgColor
        
        // Add gradient effect
        let gradientLayer = CAGradientLayer()
        gradientLayer.frame = rect
        gradientLayer.colors = [
            color.cgColor,
            color.withAlphaComponent(0.7).cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        gradientLayer.cornerRadius = 4
        
        barLayer.addSublayer(gradientLayer)
        
        // Add shadow
        barLayer.shadowColor = UIColor.black.cgColor
        barLayer.shadowOffset = CGSize(width: 0, height: 2)
        barLayer.shadowRadius = 4
        barLayer.shadowOpacity = 0.1
        
        return barLayer
    }
    
    private func drawLabels() {
        let difficulties = RouteDifficulty.allCases
        let barWidth = (bounds.width - CGFloat(difficulties.count - 1) * barSpacing) / CGFloat(difficulties.count)
        
        for (index, difficulty) in difficulties.enumerated() {
            let x = CGFloat(index) * (barWidth + barSpacing)
            let y = bounds.height - labelHeight
            
            let labelContainer = UIView()
            labelContainer.frame = CGRect(x: x, y: y, width: barWidth, height: labelHeight)
            
            let difficultyLabel = UILabel()
            difficultyLabel.text = difficulty.displayName
            difficultyLabel.font = .systemFont(ofSize: 12, weight: .semibold)
            difficultyLabel.textColor = difficulty.color
            difficultyLabel.textAlignment = .center
            difficultyLabel.numberOfLines = 2
            
            let iconImageView = UIImageView()
            iconImageView.image = UIImage(systemName: "circle.fill")
            iconImageView.tintColor = difficulty.color
            iconImageView.contentMode = .scaleAspectFit
            
            labelContainer.addSubview(iconImageView)
            labelContainer.addSubview(difficultyLabel)
            
            iconImageView.snp.makeConstraints { make in
                make.top.equalToSuperview().inset(4)
                make.centerX.equalToSuperview()
                make.width.height.equalTo(8)
            }
            
            difficultyLabel.snp.makeConstraints { make in
                make.top.equalTo(iconImageView.snp.bottom).offset(4)
                make.leading.trailing.bottom.equalToSuperview().inset(2)
            }
            
            addSubview(labelContainer)
            labelViews.append(difficultyLabel)
        }
    }
    
    private func animateBars() {
        for (index, barLayer) in barLayers.enumerated() {
            // Scale animation
            let scaleAnimation = CABasicAnimation(keyPath: "transform.scale.y")
            scaleAnimation.fromValue = 0
            scaleAnimation.toValue = 1
            scaleAnimation.duration = animationDuration
            scaleAnimation.beginTime = CACurrentMediaTime() + Double(index) * 0.1
            scaleAnimation.timingFunction = CAMediaTimingFunction(name: .easeOut)
            scaleAnimation.fillMode = .backwards
            
            // Position animation to make bars grow from bottom
            let originalPosition = barLayer.position
            barLayer.anchorPoint = CGPoint(x: 0.5, y: 1.0)
            barLayer.position = CGPoint(x: originalPosition.x, y: originalPosition.y + barLayer.bounds.height / 2)
            
            barLayer.add(scaleAnimation, forKey: "scaleAnimation")
            
            // Opacity animation for value labels
            if index < labelViews.count {
                let label = labelViews[index]
                label.alpha = 0
                
                UIView.animate(withDuration: 0.3, delay: animationDuration + Double(index) * 0.1, options: [.curveEaseOut], animations: {
                    label.alpha = 1
                })
            }
        }
    }
    
    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let location = touch.location(in: self)
        
        // Determine which bar was touched
        let difficulties = RouteDifficulty.allCases
        let barWidth = (bounds.width - CGFloat(difficulties.count - 1) * barSpacing) / CGFloat(difficulties.count)
        
        for (index, difficulty) in difficulties.enumerated() {
            let x = CGFloat(index) * (barWidth + barSpacing)
            let barRect = CGRect(x: x, y: 0, width: barWidth, height: bounds.height - labelHeight)
            
            if barRect.contains(location) {
                showBarDetails(for: difficulty, at: location)
                break
            }
        }
    }
    
    private func showBarDetails(for difficulty: RouteDifficulty, at location: CGPoint) {
        let value = difficultyData[difficulty] ?? 0
        let total = difficultyData.values.reduce(0, +)
        let percentage = total > 0 ? Double(value) / Double(total) * 100 : 0
        
        // Create tooltip
        let tooltip = UILabel()
        tooltip.text = "\(difficulty.displayName): \(value) (\(String(format: "%.1f%%", percentage)))"
        tooltip.font = .systemFont(ofSize: 12, weight: .semibold)
        tooltip.textColor = .white
        tooltip.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        tooltip.textAlignment = .center
        tooltip.layer.cornerRadius = 8
        tooltip.layer.masksToBounds = true
        tooltip.numberOfLines = 1
        
        // Size tooltip
        tooltip.sizeToFit()
        let padding: CGFloat = 12
        tooltip.frame = CGRect(
            x: 0, y: 0,
            width: tooltip.frame.width + padding * 2,
            height: tooltip.frame.height + padding
        )
        
        // Position tooltip
        var tooltipX = location.x - tooltip.frame.width / 2
        tooltipX = max(0, min(tooltipX, bounds.width - tooltip.frame.width))
        
        tooltip.frame.origin = CGPoint(x: tooltipX, y: location.y - tooltip.frame.height - 10)
        
        addSubview(tooltip)
        
        // Animate tooltip
        tooltip.alpha = 0
        tooltip.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        
        UIView.animate(withDuration: 0.2, animations: {
            tooltip.alpha = 1
            tooltip.transform = .identity
        }) { _ in
            // Remove tooltip after delay
            UIView.animate(withDuration: 0.2, delay: 2.0, options: [], animations: {
                tooltip.alpha = 0
            }) { _ in
                tooltip.removeFromSuperview()
            }
        }
    }
}
