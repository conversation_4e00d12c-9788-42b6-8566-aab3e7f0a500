//
//  Route.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import Foundation
import UIKit

// MARK: - Action Node Model
struct ActionNode: Codable {
    let id: UUID
    var name: String
    var type: ActionType
    var completionStatus: CompletionStatus
    var notes: String
    var timestamp: Date
    
    init(name: String, type: ActionType, completionStatus: CompletionStatus = .notAttempted, notes: String = "") {
        self.id = UUID()
        self.name = name
        self.type = type
        self.completionStatus = completionStatus
        self.notes = notes
        self.timestamp = Date()
    }
}

// MARK: - Action Types
enum ActionType: String, CaseIterable, Codable {
    case jump = "jump"
    case roll = "roll"
    case climb = "climb"
    case hang = "hang"
    case vault = "vault"
    case balance = "balance"
    case slide = "slide"
    case swing = "swing"
    case crawl = "crawl"
    case other = "other"
    
    var displayName: String {
        switch self {
        case .jump: return "Jump"
        case .roll: return "Roll"
        case .climb: return "Climb"
        case .hang: return "Hang"
        case .vault: return "Vault"
        case .balance: return "Balance"
        case .slide: return "Slide"
        case .swing: return "Swing"
        case .crawl: return "Crawl"
        case .other: return "Other"
        }
    }
    
    var systemIcon: String {
        switch self {
        case .jump: return "figure.jumprope"
        case .roll: return "figure.roll"
        case .climb: return "figure.climbing"
        case .hang: return "figure.strengthtraining.traditional"
        case .vault: return "figure.gymnastics"
        case .balance: return "figure.walk"
        case .slide: return "figure.skiing.downhill"
        case .swing: return "figure.swing"
        case .crawl: return "figure.crawling"
        case .other: return "figure.mixed.cardio"
        }
    }
}

// MARK: - Completion Status
enum CompletionStatus: String, CaseIterable, Codable {
    case notAttempted = "not_attempted"
    case success = "success"
    case failed = "failed"
    case delayed = "delayed"
    
    var displayName: String {
        switch self {
        case .notAttempted: return "Not Attempted"
        case .success: return "Success"
        case .failed: return "Failed"
        case .delayed: return "Delayed"
        }
    }
    
    var color: UIColor {
        switch self {
        case .notAttempted: return .systemGray
        case .success: return .systemGreen
        case .failed: return .systemRed
        case .delayed: return .systemOrange
        }
    }
    
    var systemIcon: String {
        switch self {
        case .notAttempted: return "circle"
        case .success: return "checkmark.circle.fill"
        case .failed: return "xmark.circle.fill"
        case .delayed: return "clock.circle.fill"
        }
    }
}

// MARK: - Route Difficulty
enum RouteDifficulty: Int, CaseIterable, Codable {
    case beginner = 1
    case easy = 2
    case intermediate = 3
    case hard = 4
    case expert = 5
    
    var displayName: String {
        switch self {
        case .beginner: return "Beginner"
        case .easy: return "Easy"
        case .intermediate: return "Intermediate"
        case .hard: return "Hard"
        case .expert: return "Expert"
        }
    }
    
    var color: UIColor {
        switch self {
        case .beginner: return .systemGreen
        case .easy: return .systemBlue
        case .intermediate: return .systemOrange
        case .hard: return .systemRed
        case .expert: return .systemPurple
        }
    }
}

// MARK: - Main Route Model
struct Route: Codable {
    let id: UUID
    var routeNumber: String
    var name: String
    var location: String
    var startPoint: String
    var endPoint: String
    var actionNodes: [ActionNode]
    var totalDuration: TimeInterval
    var difficulty: RouteDifficulty
    var failureNotes: String
    var routeImageData: Data?
    var createdDate: Date
    var lastModified: Date
    
    init(routeNumber: String = "", name: String, location: String, startPoint: String = "", endPoint: String = "") {
        self.id = UUID()
        self.routeNumber = routeNumber.isEmpty ? "R\(Int.random(in: 1000...9999))" : routeNumber
        self.name = name
        self.location = location
        self.startPoint = startPoint
        self.endPoint = endPoint
        self.actionNodes = []
        self.totalDuration = 0
        self.difficulty = .beginner
        self.failureNotes = ""
        self.routeImageData = nil
        self.createdDate = Date()
        self.lastModified = Date()
    }
    
    // MARK: - Computed Properties
    var completionRate: Double {
        guard !actionNodes.isEmpty else { return 0.0 }
        let successfulActions = actionNodes.filter { $0.completionStatus == .success }.count
        return Double(successfulActions) / Double(actionNodes.count)
    }
    
    var totalActions: Int {
        return actionNodes.count
    }
    
    var successfulActions: Int {
        return actionNodes.filter { $0.completionStatus == .success }.count
    }
    
    var failedActions: Int {
        return actionNodes.filter { $0.completionStatus == .failed }.count
    }
    
    var formattedDuration: String {
        let minutes = Int(totalDuration) / 60
        let seconds = Int(totalDuration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    // MARK: - Helper Methods
    mutating func addActionNode(_ actionNode: ActionNode) {
        actionNodes.append(actionNode)
        lastModified = Date()
    }
    
    mutating func removeActionNode(at index: Int) {
        guard index < actionNodes.count else { return }
        actionNodes.remove(at: index)
        lastModified = Date()
    }
    
    mutating func updateActionNode(at index: Int, with actionNode: ActionNode) {
        guard index < actionNodes.count else { return }
        actionNodes[index] = actionNode
        lastModified = Date()
    }
}
