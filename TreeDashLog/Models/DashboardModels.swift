//
//  DashboardModels.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import Foundation
import UIKit

// MARK: - Dashboard Overview Statistics
struct DashboardOverview: Codable {
    let totalTrainingSessions: Int
    let totalRoutes: Int
    let uniqueLocations: Int
    let totalTrainingTime: TimeInterval
    let overallSuccessRate: Double
    let averageRouteCompletion: Double
    let mostActiveMonth: String
    let longestStreak: Int
    let currentStreak: Int
    let lastTrainingDate: Date?
    
    var formattedTotalTime: String {
        let hours = Int(totalTrainingTime) / 3600
        let minutes = (Int(totalTrainingTime) % 3600) / 60
        return "\(hours)h \(minutes)m"
    }
    
    var formattedSuccessRate: String {
        return String(format: "%.1f%%", overallSuccessRate * 100)
    }
}

// MARK: - Route Distribution
struct RouteDistribution: Codable {
    let difficultyDistribution: [RouteDifficulty: Int]
    let locationDistribution: [String: Int]
    let actionTypeDistribution: [ActionType: Int]
    let monthlyDistribution: [String: Int]
    
    var totalRoutes: Int {
        return difficultyDistribution.values.reduce(0, +)
    }
    
    var mostPopularDifficulty: RouteDifficulty? {
        return difficultyDistribution.max { $0.value < $1.value }?.key
    }
    
    var mostPopularLocation: String? {
        return locationDistribution.max { $0.value < $1.value }?.key
    }
}

// MARK: - Success Rate Trend
struct SuccessRateTrend: Codable {
    let timeUnit: TimeUnit
    let dataPoints: [TrendDataPoint]
    
    enum TimeUnit: String, CaseIterable, Codable {
        case week = "week"
        case month = "month"
        case quarter = "quarter"
        
        var displayName: String {
            switch self {
            case .week: return "Weekly"
            case .month: return "Monthly"
            case .quarter: return "Quarterly"
            }
        }
    }
}

struct TrendDataPoint: Codable {
    let period: String
    let date: Date
    let successRate: Double
    let totalAttempts: Int
    let averageTime: TimeInterval
    let routeCount: Int
    
    var formattedPeriod: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM yyyy"
        return formatter.string(from: date)
    }
}

// MARK: - Action Performance Analysis
struct ActionPerformanceAnalysis: Codable {
    let actionType: ActionType
    let totalAttempts: Int
    let successfulAttempts: Int
    let failedAttempts: Int
    let averageTime: TimeInterval
    let trend: PerformanceTrend
    let commonFailureReasons: [String]
    
    var successRate: Double {
        guard totalAttempts > 0 else { return 0.0 }
        return Double(successfulAttempts) / Double(totalAttempts)
    }
    
    var failureRate: Double {
        return 1.0 - successRate
    }
    
    var formattedAverageTime: String {
        return String(format: "%.1fs", averageTime)
    }
}

enum PerformanceTrend: String, CaseIterable, Codable {
    case improving = "improving"
    case stable = "stable"
    case declining = "declining"
    case insufficient_data = "insufficient_data"
    
    var displayName: String {
        switch self {
        case .improving: return "Improving"
        case .stable: return "Stable"
        case .declining: return "Declining"
        case .insufficient_data: return "Insufficient Data"
        }
    }
    
    var color: UIColor {
        switch self {
        case .improving: return .systemGreen
        case .stable: return .systemBlue
        case .declining: return .systemRed
        case .insufficient_data: return .systemGray
        }
    }
    
    var systemIcon: String {
        switch self {
        case .improving: return "arrow.up.circle.fill"
        case .stable: return "minus.circle.fill"
        case .declining: return "arrow.down.circle.fill"
        case .insufficient_data: return "questionmark.circle.fill"
        }
    }
}

// MARK: - Top Failures Analysis
struct TopFailuresAnalysis: Codable {
    let topFailedActions: [FailureAnalysis]
    let failurePatterns: [FailurePattern]
    let improvementOpportunities: [ImprovementOpportunity]
    
    var totalFailures: Int {
        return topFailedActions.reduce(0) { $0 + $1.failureCount }
    }
}

struct FailureAnalysis: Codable {
    let actionType: ActionType
    let failureCount: Int
    let failureRate: Double
    let commonReasons: [String]
    let affectedRoutes: [String]
    let trend: PerformanceTrend
    
    var formattedFailureRate: String {
        return String(format: "%.1f%%", failureRate * 100)
    }
}

struct FailurePattern: Codable {
    let id: UUID
    let pattern: String
    let frequency: Int
    let affectedActions: [ActionType]
    let severity: FailureSeverity
    
    init(pattern: String, frequency: Int, affectedActions: [ActionType], severity: FailureSeverity) {
        self.id = UUID()
        self.pattern = pattern
        self.frequency = frequency
        self.affectedActions = affectedActions
        self.severity = severity
    }
}

enum FailureSeverity: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .low: return "Low Impact"
        case .medium: return "Medium Impact"
        case .high: return "High Impact"
        case .critical: return "Critical Impact"
        }
    }
    
    var color: UIColor {
        switch self {
        case .low: return .systemGreen
        case .medium: return .systemYellow
        case .high: return .systemOrange
        case .critical: return .systemRed
        }
    }
}

struct ImprovementOpportunity: Codable {
    let id: UUID
    let title: String
    let description: String
    let potentialImpact: String
    let actionTypes: [ActionType]
    let priority: Priority
    let estimatedTimeToImprove: String
    
    init(title: String, description: String, potentialImpact: String, actionTypes: [ActionType], priority: Priority, estimatedTimeToImprove: String) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.potentialImpact = potentialImpact
        self.actionTypes = actionTypes
        self.priority = priority
        self.estimatedTimeToImprove = estimatedTimeToImprove
    }
}

// MARK: - Historical Route Filter
struct HistoricalRouteFilter: Codable {
    var selectedLocations: Set<String>
    var selectedDifficulties: Set<RouteDifficulty>
    var dateRange: DateRange?
    var minSuccessRate: Double?
    var maxSuccessRate: Double?
    var sortBy: SortOption
    var sortOrder: SortOrder
    
    enum SortOption: String, CaseIterable, Codable {
        case date = "date"
        case name = "name"
        case difficulty = "difficulty"
        case successRate = "success_rate"
        case duration = "duration"
        
        var displayName: String {
            switch self {
            case .date: return "Date"
            case .name: return "Name"
            case .difficulty: return "Difficulty"
            case .successRate: return "Success Rate"
            case .duration: return "Duration"
            }
        }
    }
    
    enum SortOrder: String, CaseIterable, Codable {
        case ascending = "ascending"
        case descending = "descending"
        
        var displayName: String {
            switch self {
            case .ascending: return "Ascending"
            case .descending: return "Descending"
            }
        }
    }
    
    init() {
        self.selectedLocations = []
        self.selectedDifficulties = []
        self.dateRange = nil
        self.minSuccessRate = nil
        self.maxSuccessRate = nil
        self.sortBy = .date
        self.sortOrder = .descending
    }
}

struct DateRange: Codable {
    let startDate: Date
    let endDate: Date
    
    var isValid: Bool {
        return startDate <= endDate
    }
    
    func contains(_ date: Date) -> Bool {
        return date >= startDate && date <= endDate
    }
}

// MARK: - Training Streak
struct TrainingStreak: Codable {
    let currentStreak: Int
    let longestStreak: Int
    let streakStartDate: Date?
    let lastTrainingDate: Date?
    let streakHistory: [StreakPeriod]
    
    var isActiveStreak: Bool {
        guard let lastDate = lastTrainingDate else { return false }
        let daysSinceLastTraining = Calendar.current.dateComponents([.day], from: lastDate, to: Date()).day ?? 0
        return daysSinceLastTraining <= 7 // Consider streak active if trained within last week
    }
}

struct StreakPeriod: Codable {
    let startDate: Date
    let endDate: Date
    let streakLength: Int
    
    var duration: TimeInterval {
        return endDate.timeIntervalSince(startDate)
    }
    
    var formattedDuration: String {
        let days = Int(duration) / (24 * 3600)
        return "\(days) days"
    }
}

// MARK: - Performance Milestone
struct PerformanceMilestone: Codable {
    let id: UUID
    let title: String
    let description: String
    let achievedDate: Date
    let category: MilestoneCategory
    let value: Double
    let unit: String
    
    init(title: String, description: String, achievedDate: Date, category: MilestoneCategory, value: Double, unit: String) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.achievedDate = achievedDate
        self.category = category
        self.value = value
        self.unit = unit
    }
}

enum MilestoneCategory: String, CaseIterable, Codable {
    case routes = "routes"
    case success_rate = "success_rate"
    case time = "time"
    case streak = "streak"
    case actions = "actions"
    
    var displayName: String {
        switch self {
        case .routes: return "Routes"
        case .success_rate: return "Success Rate"
        case .time: return "Time"
        case .streak: return "Training Streak"
        case .actions: return "Actions"
        }
    }
    
    var systemIcon: String {
        switch self {
        case .routes: return "map"
        case .success_rate: return "target"
        case .time: return "clock"
        case .streak: return "flame"
        case .actions: return "figure.run"
        }
    }
    
    var color: UIColor {
        switch self {
        case .routes: return .systemBlue
        case .success_rate: return .systemGreen
        case .time: return .systemOrange
        case .streak: return .systemRed
        case .actions: return .systemPurple
        }
    }
}
