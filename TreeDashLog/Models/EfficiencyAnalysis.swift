//
//  EfficiencyAnalysis.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import Foundation
import UIKit

// MARK: - Performance Metrics
struct PerformanceMetrics: Codable {
    let routeId: UUID
    let totalAttempts: Int
    let successfulAttempts: Int
    let totalTime: TimeInterval
    let averageTime: TimeInterval
    let bestTime: TimeInterval
    let worstTime: TimeInterval
    let efficiencyScore: Double
    let improvementRate: Double
    let riskLevel: RiskLevel
    
    var successRate: Double {
        guard totalAttempts > 0 else { return 0.0 }
        return Double(successfulAttempts) / Double(totalAttempts)
    }
    
    var timeConsistency: Double {
        guard averageTime > 0 else { return 0.0 }
        let timeVariation = abs(worstTime - bestTime)
        return max(0.0, 1.0 - (timeVariation / averageTime))
    }
}

// MARK: - Action Performance
struct ActionPerformance: Codable {
    let actionType: ActionType
    let actionName: String
    let attempts: Int
    let successes: Int
    let failures: Int
    let averageTime: TimeInterval
    let totalTime: TimeInterval
    let failureReasons: [String]
    let improvementSuggestions: [String]
    
    var successRate: Double {
        guard attempts > 0 else { return 0.0 }
        return Double(successes) / Double(attempts)
    }
    
    var failureRate: Double {
        return 1.0 - successRate
    }
    
    var efficiencyRating: EfficiencyRating {
        let score = successRate * 0.7 + (1.0 - min(averageTime / 30.0, 1.0)) * 0.3
        
        switch score {
        case 0.9...1.0: return .excellent
        case 0.7..<0.9: return .good
        case 0.5..<0.7: return .average
        case 0.3..<0.5: return .poor
        default: return .critical
        }
    }
}

// MARK: - Risk Assessment
enum RiskLevel: String, CaseIterable, Codable {
    case low = "low"
    case moderate = "moderate"
    case high = "high"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .low: return "Low Risk"
        case .moderate: return "Moderate Risk"
        case .high: return "High Risk"
        case .critical: return "Critical Risk"
        }
    }
    
    var color: UIColor {
        switch self {
        case .low: return .systemGreen
        case .moderate: return .systemYellow
        case .high: return .systemOrange
        case .critical: return .systemRed
        }
    }
    
    var systemIcon: String {
        switch self {
        case .low: return "checkmark.shield.fill"
        case .moderate: return "exclamationmark.shield.fill"
        case .high: return "xmark.shield.fill"
        case .critical: return "exclamationmark.triangle.fill"
        }
    }
}

// MARK: - Efficiency Rating
enum EfficiencyRating: String, CaseIterable, Codable {
    case excellent = "excellent"
    case good = "good"
    case average = "average"
    case poor = "poor"
    case critical = "critical"
    
    var displayName: String {
        switch self {
        case .excellent: return "Excellent"
        case .good: return "Good"
        case .average: return "Average"
        case .poor: return "Poor"
        case .critical: return "Critical"
        }
    }
    
    var color: UIColor {
        switch self {
        case .excellent: return .systemGreen
        case .good: return .systemBlue
        case .average: return .systemYellow
        case .poor: return .systemOrange
        case .critical: return .systemRed
        }
    }
    
    var scoreRange: String {
        switch self {
        case .excellent: return "90-100%"
        case .good: return "70-89%"
        case .average: return "50-69%"
        case .poor: return "30-49%"
        case .critical: return "0-29%"
        }
    }
}

// MARK: - Improvement Suggestion
struct ImprovementSuggestion: Codable {
    let id: UUID
    let actionType: ActionType
    let priority: Priority
    let category: SuggestionCategory
    let title: String
    let description: String
    let expectedImprovement: String
    let difficulty: SuggestionDifficulty
    
    init(actionType: ActionType, priority: Priority, category: SuggestionCategory, title: String, description: String, expectedImprovement: String, difficulty: SuggestionDifficulty) {
        self.id = UUID()
        self.actionType = actionType
        self.priority = priority
        self.category = category
        self.title = title
        self.description = description
        self.expectedImprovement = expectedImprovement
        self.difficulty = difficulty
    }
}

enum Priority: String, CaseIterable, Codable {
    case high = "high"
    case medium = "medium"
    case low = "low"
    
    var displayName: String {
        switch self {
        case .high: return "High Priority"
        case .medium: return "Medium Priority"
        case .low: return "Low Priority"
        }
    }
    
    var color: UIColor {
        switch self {
        case .high: return .systemRed
        case .medium: return .systemOrange
        case .low: return .systemBlue
        }
    }
}

enum SuggestionCategory: String, CaseIterable, Codable {
    case technique = "technique"
    case timing = "timing"
    case strength = "strength"
    case flexibility = "flexibility"
    case mental = "mental"
    
    var displayName: String {
        switch self {
        case .technique: return "Technique"
        case .timing: return "Timing"
        case .strength: return "Strength"
        case .flexibility: return "Flexibility"
        case .mental: return "Mental Focus"
        }
    }
    
    var systemIcon: String {
        switch self {
        case .technique: return "gearshape.fill"
        case .timing: return "clock.fill"
        case .strength: return "dumbbell.fill"
        case .flexibility: return "figure.flexibility"
        case .mental: return "brain.head.profile"
        }
    }
}

enum SuggestionDifficulty: String, CaseIterable, Codable {
    case easy = "easy"
    case medium = "medium"
    case hard = "hard"
    
    var displayName: String {
        switch self {
        case .easy: return "Easy"
        case .medium: return "Medium"
        case .hard: return "Hard"
        }
    }
    
    var color: UIColor {
        switch self {
        case .easy: return .systemGreen
        case .medium: return .systemYellow
        case .hard: return .systemRed
        }
    }
}

// MARK: - Success Rate Data Point
struct SuccessRateDataPoint: Codable {
    let attemptNumber: Int
    let successRate: Double
    let timestamp: Date
    let cumulativeTime: TimeInterval
    
    init(attemptNumber: Int, successRate: Double, cumulativeTime: TimeInterval) {
        self.attemptNumber = attemptNumber
        self.successRate = successRate
        self.timestamp = Date()
        self.cumulativeTime = cumulativeTime
    }
}

// MARK: - Efficiency Analysis Result
struct EfficiencyAnalysisResult: Codable {
    let routeId: UUID
    let routeName: String
    let analysisDate: Date
    let overallMetrics: PerformanceMetrics
    let actionPerformances: [ActionPerformance]
    let improvementSuggestions: [ImprovementSuggestion]
    let successRateCurve: [SuccessRateDataPoint]
    let timeDistribution: [ActionType: TimeInterval]
    let riskAssessment: RiskAssessment
    
    var mostProblematicAction: ActionPerformance? {
        return actionPerformances.min { $0.successRate < $1.successRate }
    }
    
    var mostTimeConsumingAction: ActionPerformance? {
        return actionPerformances.max { $0.averageTime < $1.averageTime }
    }
    
    var highPrioritySuggestions: [ImprovementSuggestion] {
        return improvementSuggestions.filter { $0.priority == .high }
    }
}

// MARK: - Risk Assessment
struct RiskAssessment: Codable {
    let overallRisk: RiskLevel
    let riskFactors: [RiskFactor]
    let mitigationStrategies: [String]
    
    var criticalRiskFactors: [RiskFactor] {
        return riskFactors.filter { $0.severity == .critical }
    }
}

struct RiskFactor: Codable {
    let id: UUID
    let actionType: ActionType
    let description: String
    let severity: RiskLevel
    let likelihood: Double
    let impact: Double
    let mitigationSuggestion: String
    
    init(actionType: ActionType, description: String, severity: RiskLevel, likelihood: Double, impact: Double, mitigationSuggestion: String) {
        self.id = UUID()
        self.actionType = actionType
        self.description = description
        self.severity = severity
        self.likelihood = likelihood
        self.impact = impact
        self.mitigationSuggestion = mitigationSuggestion
    }
    
    var riskScore: Double {
        return likelihood * impact
    }
}
