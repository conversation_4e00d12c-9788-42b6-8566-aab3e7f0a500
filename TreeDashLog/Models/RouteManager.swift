//
//  RouteManager.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import Foundation

class RouteManager {
    static let shared = RouteManager()
    private let userDefaults = UserDefaults.standard
    private let routesKey = "SavedRoutes"
    
    private init() {}
    
    // MARK: - Route Management
    func saveRoute(_ route: Route) {
        var routes = getAllRoutes()
        
        // Check if route already exists and update it
        if let index = routes.firstIndex(where: { $0.id == route.id }) {
            routes[index] = route
        } else {
            routes.append(route)
        }
        
        saveRoutes(routes)
    }
    
    func getAllRoutes() -> [Route] {
        guard let data = userDefaults.data(forKey: routesKey) else {
            return []
        }
        
        do {
            let routes = try JSONDecoder().decode([Route].self, from: data)
            return routes.sorted { $0.lastModified > $1.lastModified }
        } catch {
            print("Error decoding routes: \(error)")
            return []
        }
    }
    
    func deleteRoute(withId id: UUID) {
        var routes = getAllRoutes()
        routes.removeAll { $0.id == id }
        saveRoutes(routes)
    }
    
    func getRoute(withId id: UUID) -> Route? {
        return getAllRoutes().first { $0.id == id }
    }
    
    private func saveRoutes(_ routes: [Route]) {
        do {
            let data = try JSONEncoder().encode(routes)
            userDefaults.set(data, forKey: routesKey)
        } catch {
            print("Error encoding routes: \(error)")
        }
    }
    
    // MARK: - Statistics
    func getTotalRoutes() -> Int {
        return getAllRoutes().count
    }
    
    func getAverageCompletionRate() -> Double {
        let routes = getAllRoutes()
        guard !routes.isEmpty else { return 0.0 }
        
        let totalCompletionRate = routes.reduce(0.0) { $0 + $1.completionRate }
        return totalCompletionRate / Double(routes.count)
    }
    
    func getRoutesByDifficulty(_ difficulty: RouteDifficulty) -> [Route] {
        return getAllRoutes().filter { $0.difficulty == difficulty }
    }
    
    func getRecentRoutes(limit: Int = 5) -> [Route] {
        let routes = getAllRoutes()
        return Array(routes.prefix(limit))
    }
    
    // MARK: - Search and Filter
    func searchRoutes(query: String) -> [Route] {
        let routes = getAllRoutes()
        guard !query.isEmpty else { return routes }
        
        return routes.filter { route in
            route.name.localizedCaseInsensitiveContains(query) ||
            route.location.localizedCaseInsensitiveContains(query) ||
            route.routeNumber.localizedCaseInsensitiveContains(query)
        }
    }
    
    func filterRoutes(by difficulty: RouteDifficulty? = nil, 
                     minCompletionRate: Double? = nil) -> [Route] {
        var routes = getAllRoutes()
        
        if let difficulty = difficulty {
            routes = routes.filter { $0.difficulty == difficulty }
        }
        
        if let minRate = minCompletionRate {
            routes = routes.filter { $0.completionRate >= minRate }
        }
        
        return routes
    }
}
