//
//  RouteManager.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import Foundation

class RouteManager {
    static let shared = RouteManager()
    private let userDefaults = UserDefaults.standard
    private let routesKey = "SavedRoutes"
    private let firstLaunchKey = "HasLaunchedBefore"

    private init() {
        checkFirstLaunch()
    }
    
    // MARK: - Route Management
    func saveRoute(_ route: Route) {
        var routes = getAllRoutes()
        
        // Check if route already exists and update it
        if let index = routes.firstIndex(where: { $0.id == route.id }) {
            routes[index] = route
        } else {
            routes.append(route)
        }
        
        saveRoutes(routes)
    }
    
    func getAllRoutes() -> [Route] {
        guard let data = userDefaults.data(forKey: routesKey) else {
            return []
        }
        
        do {
            let routes = try JSONDecoder().decode([Route].self, from: data)
            return routes.sorted { $0.lastModified > $1.lastModified }
        } catch {
            print("Error decoding routes: \(error)")
            return []
        }
    }
    
    func deleteRoute(withId id: UUID) {
        var routes = getAllRoutes()
        routes.removeAll { $0.id == id }
        saveRoutes(routes)
    }
    
    func getRoute(withId id: UUID) -> Route? {
        return getAllRoutes().first { $0.id == id }
    }
    
    private func saveRoutes(_ routes: [Route]) {
        do {
            let data = try JSONEncoder().encode(routes)
            userDefaults.set(data, forKey: routesKey)
        } catch {
            print("Error encoding routes: \(error)")
        }
    }
    
    // MARK: - Statistics
    func getTotalRoutes() -> Int {
        return getAllRoutes().count
    }
    
    func getAverageCompletionRate() -> Double {
        let routes = getAllRoutes()
        guard !routes.isEmpty else { return 0.0 }
        
        let totalCompletionRate = routes.reduce(0.0) { $0 + $1.completionRate }
        return totalCompletionRate / Double(routes.count)
    }
    
    func getRoutesByDifficulty(_ difficulty: RouteDifficulty) -> [Route] {
        return getAllRoutes().filter { $0.difficulty == difficulty }
    }
    
    func getRecentRoutes(limit: Int = 5) -> [Route] {
        let routes = getAllRoutes()
        return Array(routes.prefix(limit))
    }
    
    // MARK: - Search and Filter
    func searchRoutes(query: String) -> [Route] {
        let routes = getAllRoutes()
        guard !query.isEmpty else { return routes }
        
        return routes.filter { route in
            route.name.localizedCaseInsensitiveContains(query) ||
            route.location.localizedCaseInsensitiveContains(query) ||
            route.routeNumber.localizedCaseInsensitiveContains(query)
        }
    }
    
    func filterRoutes(by difficulty: RouteDifficulty? = nil, 
                     minCompletionRate: Double? = nil) -> [Route] {
        var routes = getAllRoutes()
        
        if let difficulty = difficulty {
            routes = routes.filter { $0.difficulty == difficulty }
        }
        
        if let minRate = minCompletionRate {
            routes = routes.filter { $0.completionRate >= minRate }
        }
        
        return routes
    }

    // MARK: - First Launch Setup
    private func checkFirstLaunch() {
        let hasLaunchedBefore = userDefaults.bool(forKey: firstLaunchKey)

        if !hasLaunchedBefore {
            createSampleData()
            userDefaults.set(true, forKey: firstLaunchKey)
        }
    }

    private func createSampleData() {
        let sampleRoutes = generateSampleRoutes()
        saveRoutes(sampleRoutes)
    }

    private func generateSampleRoutes() -> [Route] {
        var routes: [Route] = []

        // Sample Route 1: Beginner Forest Trail
        let route1 = createBeginnerForestRoute()
        routes.append(route1)

        // Sample Route 2: Intermediate Urban Course
        let route2 = createIntermediateUrbanRoute()
        routes.append(route2)

        // Sample Route 3: Advanced Mountain Trail
        let route3 = createAdvancedMountainRoute()
        routes.append(route3)

        return routes
    }

    private func createBeginnerForestRoute() -> Route {
        let baseDate = Calendar.current.date(byAdding: .day, value: -7, to: Date()) ?? Date()

        var route = Route(name: "Forest Trail Basics")
        route.routeNumber = "FT-001"
        route.location = "Central Park Forest"
        route.startPoint = "Main Trail Entrance"
        route.endPoint = "Forest Clearing"
        route.difficulty = .beginner
        route.notes = "Perfect for learning basic parkour movements in a natural environment"
        route.createdDate = baseDate
        route.lastModified = baseDate

        // Add realistic action nodes
        let actions = [
            createActionNode(type: .jump, name: "Log Jump", status: .success, baseDate: baseDate, offset: 30),
            createActionNode(type: .balance, name: "Fallen Tree Balance", status: .success, baseDate: baseDate, offset: 90),
            createActionNode(type: .climb, name: "Rock Climb", status: .success, baseDate: baseDate, offset: 180),
            createActionNode(type: .roll, name: "Grass Roll", status: .success, baseDate: baseDate, offset: 240),
            createActionNode(type: .vault, name: "Low Branch Vault", status: .delayed, baseDate: baseDate, offset: 320),
            createActionNode(type: .jump, name: "Stream Jump", status: .success, baseDate: baseDate, offset: 400)
        ]

        route.actionNodes = actions
        route.totalDuration = 450 // 7.5 minutes

        return route
    }

    private func createIntermediateUrbanRoute() -> Route {
        let baseDate = Calendar.current.date(byAdding: .day, value: -3, to: Date()) ?? Date()

        var route = Route(name: "Urban Playground")
        route.routeNumber = "UP-002"
        route.location = "Downtown Sports Complex"
        route.startPoint = "Basketball Court"
        route.endPoint = "Skate Park"
        route.difficulty = .intermediate
        route.notes = "Urban environment with concrete obstacles and metal structures"
        route.createdDate = baseDate
        route.lastModified = baseDate

        // Add realistic action nodes with some failures
        let actions = [
            createActionNode(type: .vault, name: "Bench Vault", status: .success, baseDate: baseDate, offset: 20),
            createActionNode(type: .jump, name: "Precision Jump", status: .success, baseDate: baseDate, offset: 60),
            createActionNode(type: .climb, name: "Wall Climb", status: .failed, baseDate: baseDate, offset: 120),
            createActionNode(type: .climb, name: "Wall Climb (Retry)", status: .success, baseDate: baseDate, offset: 180),
            createActionNode(type: .balance, name: "Rail Balance", status: .success, baseDate: baseDate, offset: 240),
            createActionNode(type: .roll, name: "Concrete Roll", status: .delayed, baseDate: baseDate, offset: 300),
            createActionNode(type: .swing, name: "Bar Swing", status: .success, baseDate: baseDate, offset: 360),
            createActionNode(type: .jump, name: "Gap Jump", status: .failed, baseDate: baseDate, offset: 420),
            createActionNode(type: .vault, name: "Final Vault", status: .success, baseDate: baseDate, offset: 480)
        ]

        route.actionNodes = actions
        route.totalDuration = 520 // 8.7 minutes

        return route
    }

    private func createAdvancedMountainRoute() -> Route {
        let baseDate = Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()

        var route = Route(name: "Mountain Challenge")
        route.routeNumber = "MC-003"
        route.location = "Rocky Mountain Trail"
        route.startPoint = "Trailhead Parking"
        route.endPoint = "Summit Viewpoint"
        route.difficulty = .expert
        route.notes = "Challenging mountain terrain with natural rock formations and steep inclines"
        route.createdDate = baseDate
        route.lastModified = baseDate

        // Add realistic action nodes with higher difficulty
        let actions = [
            createActionNode(type: .climb, name: "Boulder Climb", status: .success, baseDate: baseDate, offset: 45),
            createActionNode(type: .jump, name: "Rock Gap Jump", status: .success, baseDate: baseDate, offset: 120),
            createActionNode(type: .balance, name: "Ridge Balance", status: .delayed, baseDate: baseDate, offset: 200),
            createActionNode(type: .climb, name: "Steep Rock Face", status: .failed, baseDate: baseDate, offset: 300),
            createActionNode(type: .climb, name: "Alternative Route", status: .success, baseDate: baseDate, offset: 420),
            createActionNode(type: .crawl, name: "Cave Crawl", status: .success, baseDate: baseDate, offset: 480),
            createActionNode(type: .jump, name: "Ledge Jump", status: .success, baseDate: baseDate, offset: 540),
            createActionNode(type: .hang, name: "Cliff Hang", status: .delayed, baseDate: baseDate, offset: 600),
            createActionNode(type: .climb, name: "Final Ascent", status: .success, baseDate: baseDate, offset: 720),
            createActionNode(type: .balance, name: "Summit Balance", status: .success, baseDate: baseDate, offset: 780)
        ]

        route.actionNodes = actions
        route.totalDuration = 820 // 13.7 minutes

        return route
    }

    private func createActionNode(type: ActionType, name: String, status: CompletionStatus, baseDate: Date, offset: TimeInterval) -> ActionNode {
        let actionDate = baseDate.addingTimeInterval(offset)

        var actionNode = ActionNode(type: type, name: name)
        actionNode.completionStatus = status
        actionNode.timestamp = actionDate

        // Add realistic notes for failed actions
        if status == .failed {
            switch type {
            case .jump:
                actionNode.notes = "Misjudged distance, landed short"
            case .climb:
                actionNode.notes = "Lost grip, need to improve upper body strength"
            case .balance:
                actionNode.notes = "Lost focus, fell after 3 seconds"
            case .vault:
                actionNode.notes = "Hesitated too long, awkward landing"
            default:
                actionNode.notes = "Technical execution issue, need more practice"
            }
        } else if status == .delayed {
            actionNode.notes = "Took extra time to assess safety and plan approach"
        }

        return actionNode
    }

    // MARK: - Development/Testing Methods
    func resetFirstLaunchFlag() {
        userDefaults.removeObject(forKey: firstLaunchKey)
        userDefaults.removeObject(forKey: routesKey)
    }

    func recreateSampleData() {
        resetFirstLaunchFlag()
        checkFirstLaunch()
    }
}
