//
//  TrainingStatisticsViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class TrainingStatisticsViewController: UIViewController {
    
    // MARK: - Properties
    private var dashboardOverview: DashboardOverview?
    private var routeDistribution: RouteDistribution?
    private var trainingStreak: TrainingStreak?
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .systemGroupedBackground
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // Overview Section
    private lazy var overviewContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var overviewStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 1
        stack.backgroundColor = .systemGray5
        return stack
    }()
    
    // Distribution Charts Section
    private lazy var distributionContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var difficultyChartView: DifficultyDistributionChartView = {
        let chart = DifficultyDistributionChartView()
        chart.backgroundColor = .clear
        return chart
    }()
    
    // Location Distribution Section
    private lazy var locationContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var locationTableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.register(LocationDistributionTableViewCell.self, forCellReuseIdentifier: LocationDistributionTableViewCell.identifier)
        table.separatorStyle = .none
        table.isScrollEnabled = false
        return table
    }()
    
    // Streak Section
    private lazy var streakContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var currentStreakLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 36, weight: .bold)
        label.textColor = .systemRed
        label.textAlignment = .center
        return label
    }()
    
    private lazy var streakDescriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "Current Training Streak"
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        label.textAlignment = .center
        return label
    }()
    
    private lazy var longestStreakLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadStatisticsData()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = "Training Statistics"
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "square.and.arrow.up"),
            style: .plain,
            target: self,
            action: #selector(shareStatistics)
        )
        
        setupScrollView()
        setupConstraints()
    }
    
    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        [overviewContainer, distributionContainer, locationContainer, streakContainer].forEach {
            contentView.addSubview($0)
        }
        
        setupOverviewSection()
        setupDistributionSection()
        setupLocationSection()
        setupStreakSection()
    }
    
    private func setupOverviewSection() {
        let titleLabel = createSectionTitleLabel("Training Overview")
        
        [titleLabel, overviewStackView].forEach {
            overviewContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        overviewStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupDistributionSection() {
        let titleLabel = createSectionTitleLabel("Difficulty Distribution")
        
        [titleLabel, difficultyChartView].forEach {
            distributionContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        difficultyChartView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(200)
        }
    }
    
    private func setupLocationSection() {
        let titleLabel = createSectionTitleLabel("Training Locations")
        
        [titleLabel, locationTableView].forEach {
            locationContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        locationTableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(0) // Will be updated dynamically
        }
    }
    
    private func setupStreakSection() {
        let titleLabel = createSectionTitleLabel("Training Streak")
        let streakIcon = UIImageView()
        streakIcon.image = UIImage(systemName: "flame.fill")
        streakIcon.tintColor = .systemRed
        streakIcon.contentMode = .scaleAspectFit
        
        [titleLabel, streakIcon, currentStreakLabel, streakDescriptionLabel, longestStreakLabel].forEach {
            streakContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        streakIcon.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(40)
        }
        
        currentStreakLabel.snp.makeConstraints { make in
            make.top.equalTo(streakIcon.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
        }
        
        streakDescriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(currentStreakLabel.snp.bottom).offset(4)
            make.centerX.equalToSuperview()
        }
        
        longestStreakLabel.snp.makeConstraints { make in
            make.top.equalTo(streakDescriptionLabel.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        overviewContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        distributionContainer.snp.makeConstraints { make in
            make.top.equalTo(overviewContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        locationContainer.snp.makeConstraints { make in
            make.top.equalTo(distributionContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        streakContainer.snp.makeConstraints { make in
            make.top.equalTo(locationContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .systemPurple
        return label
    }
    
    // MARK: - Data Loading
    private func loadStatisticsData() {
        DispatchQueue.global(qos: .userInitiated).async {
            let overview = DashboardAnalyticsEngine.shared.generateDashboardOverview()
            let distribution = DashboardAnalyticsEngine.shared.generateRouteDistribution()
            let streak = DashboardAnalyticsEngine.shared.generateTrainingStreak()
            
            DispatchQueue.main.async {
                self.dashboardOverview = overview
                self.routeDistribution = distribution
                self.trainingStreak = streak
                self.updateUI()
            }
        }
    }
    
    private func updateUI() {
        guard let overview = dashboardOverview,
              let distribution = routeDistribution,
              let streak = trainingStreak else { return }
        
        setupOverviewData(overview)
        difficultyChartView.configure(with: distribution.difficultyDistribution)
        updateLocationTableHeight()
        updateStreakData(streak)
        
        // Animate appearance
        animateContentAppearance()
    }

    private func setupOverviewData(_ overview: DashboardOverview) {
        let overviewRows = [
            createOverviewRow(title: "Total Training Sessions", value: "\(overview.totalTrainingSessions)", color: .systemBlue),
            createOverviewRow(title: "Unique Routes", value: "\(overview.totalRoutes)", color: .systemGreen),
            createOverviewRow(title: "Training Locations", value: "\(overview.uniqueLocations)", color: .systemOrange),
            createOverviewRow(title: "Total Training Time", value: overview.formattedTotalTime, color: .systemPurple),
            createOverviewRow(title: "Overall Success Rate", value: overview.formattedSuccessRate, color: .systemRed),
            createOverviewRow(title: "Average Completion", value: String(format: "%.1f%%", overview.averageRouteCompletion * 100), color: .systemTeal),
            createOverviewRow(title: "Most Active Month", value: overview.mostActiveMonth, color: .systemIndigo)
        ]

        overviewRows.forEach { overviewStackView.addArrangedSubview($0) }
    }

    private func createOverviewRow(title: String, value: String, color: UIColor) -> UIView {
        let container = UIView()
        container.backgroundColor = .systemBackground

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = .label

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 16, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .right

        [titleLabel, valueLabel].forEach { container.addSubview($0) }

        titleLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview().inset(16)
        }

        valueLabel.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview().inset(16)
        }

        container.snp.makeConstraints { make in
            make.height.equalTo(50)
        }

        return container
    }

    private func updateLocationTableHeight() {
        guard let distribution = routeDistribution else { return }
        let height = CGFloat(distribution.locationDistribution.count * 50)
        locationTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
        locationTableView.reloadData()
    }

    private func updateStreakData(_ streak: TrainingStreak) {
        currentStreakLabel.text = "\(streak.currentStreak)"
        longestStreakLabel.text = "Longest streak: \(streak.longestStreak) sessions"

        // Update streak icon color based on current streak
        if let streakIcon = streakContainer.subviews.compactMap({ $0 as? UIImageView }).first {
            if streak.currentStreak > 0 {
                streakIcon.tintColor = .systemRed
                currentStreakLabel.textColor = .systemRed
            } else {
                streakIcon.tintColor = .systemGray
                currentStreakLabel.textColor = .systemGray
            }
        }
    }

    private func animateContentAppearance() {
        let containers = [overviewContainer, distributionContainer, locationContainer, streakContainer]

        containers.enumerated().forEach { index, container in
            container.alpha = 0
            container.transform = CGAffineTransform(translationX: 0, y: 30)

            UIView.animate(withDuration: 0.6, delay: Double(index) * 0.1, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [.curveEaseOut], animations: {
                container.alpha = 1
                container.transform = .identity
            })
        }
    }

    // MARK: - Actions
    @objc private func shareStatistics() {
        guard let overview = dashboardOverview else { return }

        let shareText = """
        TreeDash Log - Training Statistics

        📊 Training Overview:
        • Total Sessions: \(overview.totalTrainingSessions)
        • Unique Routes: \(overview.totalRoutes)
        • Training Locations: \(overview.uniqueLocations)
        • Total Time: \(overview.formattedTotalTime)
        • Success Rate: \(overview.formattedSuccessRate)
        • Current Streak: \(overview.currentStreak) sessions
        • Longest Streak: \(overview.longestStreak) sessions

        Keep pushing your limits! 🏃‍♂️💪
        """

        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)

        if let popover = activityVC.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItem
        }

        present(activityVC, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension TrainingStatisticsViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return routeDistribution?.locationDistribution.count ?? 0
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: LocationDistributionTableViewCell.identifier, for: indexPath) as? LocationDistributionTableViewCell,
              let distribution = routeDistribution else {
            return UITableViewCell()
        }

        let sortedLocations = distribution.locationDistribution.sorted { $0.value > $1.value }
        let location = sortedLocations[indexPath.row]
        cell.configure(location: location.key, count: location.value, total: distribution.totalRoutes)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension TrainingStatisticsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 50
    }
}

// MARK: - Location Distribution Cell
class LocationDistributionTableViewCell: UITableViewCell {
    static let identifier = "LocationDistributionTableViewCell"

    private let locationLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .label
        return label
    }()

    private let countLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .semibold)
        label.textColor = .systemPurple
        label.textAlignment = .right
        return label
    }()

    private let progressView: UIProgressView = {
        let progress = UIProgressView(progressViewStyle: .default)
        progress.progressTintColor = .systemPurple
        progress.trackTintColor = .systemGray5
        progress.layer.cornerRadius = 2
        progress.clipsToBounds = true
        return progress
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        [locationLabel, countLabel, progressView].forEach {
            contentView.addSubview($0)
        }

        locationLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.top.equalToSuperview().inset(8)
            make.trailing.equalTo(countLabel.snp.leading).offset(-8)
        }

        countLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalTo(locationLabel)
            make.width.equalTo(60)
        }

        progressView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview().inset(16)
            make.top.equalTo(locationLabel.snp.bottom).offset(4)
            make.bottom.equalToSuperview().inset(8)
            make.height.equalTo(4)
        }
    }

    func configure(location: String, count: Int, total: Int) {
        locationLabel.text = location
        countLabel.text = "\(count)"

        let percentage = total > 0 ? Float(count) / Float(total) : 0
        progressView.progress = percentage
    }
}
