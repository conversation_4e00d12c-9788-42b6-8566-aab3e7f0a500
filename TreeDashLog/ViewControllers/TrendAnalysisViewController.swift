//
//  TrendAnalysisViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class TrendAnalysisViewController: UIViewController {
    
    // MARK: - Properties
    private var successRateTrend: SuccessRateTrend?
    private var actionPerformances: [ActionPerformanceAnalysis] = []
    private var selectedTimeUnit: SuccessRateTrend.TimeUnit = .month
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .systemGroupedBackground
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // Time Unit Selector
    private lazy var timeUnitContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var timeUnitSegmentedControl: UISegmentedControl = {
        let items = SuccessRateTrend.TimeUnit.allCases.map { $0.displayName }
        let control = UISegmentedControl(items: items)
        control.selectedSegmentIndex = 1 // Month
        control.backgroundColor = .systemBackground
        control.selectedSegmentTintColor = .systemPurple
        control.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        control.addTarget(self, action: #selector(timeUnitChanged), for: .valueChanged)
        return control
    }()
    
    // Success Rate Trend Chart
    private lazy var trendChartContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var trendChartView: TrendChartView = {
        let chart = TrendChartView()
        chart.backgroundColor = .clear
        return chart
    }()
    
    // Action Performance Trends
    private lazy var actionTrendsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var actionTrendsTableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.register(ActionTrendTableViewCell.self, forCellReuseIdentifier: ActionTrendTableViewCell.identifier)
        table.separatorStyle = .none
        table.isScrollEnabled = false
        return table
    }()
    
    // Insights Section
    private lazy var insightsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var insightsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .label
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadTrendData()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = "Trend Analysis"
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "square.and.arrow.up"),
            style: .plain,
            target: self,
            action: #selector(shareTrends)
        )
        
        setupScrollView()
        setupConstraints()
    }
    
    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        [timeUnitContainer, trendChartContainer, actionTrendsContainer, insightsContainer].forEach {
            contentView.addSubview($0)
        }
        
        setupTimeUnitSection()
        setupTrendChartSection()
        setupActionTrendsSection()
        setupInsightsSection()
    }
    
    private func setupTimeUnitSection() {
        let titleLabel = createSectionTitleLabel("Time Period")
        
        [titleLabel, timeUnitSegmentedControl].forEach {
            timeUnitContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        timeUnitSegmentedControl.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }
    }
    
    private func setupTrendChartSection() {
        let titleLabel = createSectionTitleLabel("Success Rate Trend")
        
        [titleLabel, trendChartView].forEach {
            trendChartContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        trendChartView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(250)
        }
    }
    
    private func setupActionTrendsSection() {
        let titleLabel = createSectionTitleLabel("Action Performance Trends")
        
        [titleLabel, actionTrendsTableView].forEach {
            actionTrendsContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        actionTrendsTableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(0) // Will be updated dynamically
        }
    }
    
    private func setupInsightsSection() {
        let titleLabel = createSectionTitleLabel("Trend Insights")
        
        [titleLabel, insightsLabel].forEach {
            insightsContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        insightsLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        timeUnitContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        trendChartContainer.snp.makeConstraints { make in
            make.top.equalTo(timeUnitContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        actionTrendsContainer.snp.makeConstraints { make in
            make.top.equalTo(trendChartContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        insightsContainer.snp.makeConstraints { make in
            make.top.equalTo(actionTrendsContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .systemPurple
        return label
    }
    
    // MARK: - Data Loading
    private func loadTrendData() {
        DispatchQueue.global(qos: .userInitiated).async {
            let trend = DashboardAnalyticsEngine.shared.generateSuccessRateTrend(timeUnit: self.selectedTimeUnit)
            let performances = DashboardAnalyticsEngine.shared.generateActionPerformanceAnalysis()
            
            DispatchQueue.main.async {
                self.successRateTrend = trend
                self.actionPerformances = performances
                self.updateUI()
            }
        }
    }
    
    private func updateUI() {
        guard let trend = successRateTrend else { return }
        
        trendChartView.configure(with: trend)
        updateActionTrendsTableHeight()
        generateInsights()
        
        // Animate appearance
        animateContentAppearance()
    }
    
    private func updateActionTrendsTableHeight() {
        let height = CGFloat(min(actionPerformances.count, 5) * 70) // Show max 5 items
        actionTrendsTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
        actionTrendsTableView.reloadData()
    }
    
    private func generateInsights() {
        guard let trend = successRateTrend else { return }
        
        var insights: [String] = []
        
        if trend.dataPoints.count >= 2 {
            let firstPoint = trend.dataPoints.first!
            let lastPoint = trend.dataPoints.last!
            let improvement = (lastPoint.successRate - firstPoint.successRate) * 100
            
            if improvement > 10 {
                insights.append("🎉 Excellent progress! Your success rate improved by \(String(format: "%.1f%%", improvement)) over this period.")
            } else if improvement > 0 {
                insights.append("📈 Good improvement! Your success rate increased by \(String(format: "%.1f%%", improvement)).")
            } else if improvement < -10 {
                insights.append("⚠️ Performance decline detected. Consider reviewing your training approach.")
            } else {
                insights.append("📊 Your performance has been relatively stable during this period.")
            }
        }
        
        // Action trend insights
        let improvingActions = actionPerformances.filter { $0.trend == .improving }
        let decliningActions = actionPerformances.filter { $0.trend == .declining }
        
        if !improvingActions.isEmpty {
            let actionNames = improvingActions.prefix(3).map { $0.actionType.displayName }.joined(separator: ", ")
            insights.append("💪 Improving actions: \(actionNames)")
        }
        
        if !decliningActions.isEmpty {
            let actionNames = decliningActions.prefix(2).map { $0.actionType.displayName }.joined(separator: ", ")
            insights.append("🔄 Actions needing attention: \(actionNames)")
        }
        
        insightsLabel.text = insights.joined(separator: "\n\n")
    }
    
    private func animateContentAppearance() {
        let containers = [timeUnitContainer, trendChartContainer, actionTrendsContainer, insightsContainer]
        
        containers.enumerated().forEach { index, container in
            container.alpha = 0
            container.transform = CGAffineTransform(translationX: 0, y: 30)
            
            UIView.animate(withDuration: 0.6, delay: Double(index) * 0.1, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [.curveEaseOut], animations: {
                container.alpha = 1
                container.transform = .identity
            })
        }
    }
    
    // MARK: - Actions
    @objc private func timeUnitChanged() {
        selectedTimeUnit = SuccessRateTrend.TimeUnit.allCases[timeUnitSegmentedControl.selectedSegmentIndex]
        loadTrendData()
    }
    
    @objc private func shareTrends() {
        guard let trend = successRateTrend else { return }
        
        let shareText = """
        TreeDash Log - Trend Analysis (\(trend.timeUnit.displayName))
        
        📈 Success Rate Trend:
        • Data Points: \(trend.dataPoints.count)
        • Time Period: \(trend.timeUnit.displayName)
        
        🎯 Action Performance Trends:
        \(actionPerformances.prefix(5).map { "• \($0.actionType.displayName): \($0.trend.displayName)" }.joined(separator: "\n"))
        
        Keep tracking your progress! 🏃‍♂️📊
        """
        
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
        
        if let popover = activityVC.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItem
        }
        
        present(activityVC, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension TrendAnalysisViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return min(actionPerformances.count, 5) // Show max 5 items
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: ActionTrendTableViewCell.identifier, for: indexPath) as? ActionTrendTableViewCell else {
            return UITableViewCell()
        }

        let performance = actionPerformances[indexPath.row]
        cell.configure(with: performance)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension TrendAnalysisViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let performance = actionPerformances[indexPath.row]
        showActionTrendDetail(performance)
    }

    private func showActionTrendDetail(_ performance: ActionPerformanceAnalysis) {
        let alert = UIAlertController(
            title: "\(performance.actionType.displayName) Trend",
            message: """
            Performance: \(performance.trend.displayName)
            Success Rate: \(String(format: "%.1f%%", performance.successRate * 100))
            Average Time: \(performance.formattedAverageTime)
            Total Attempts: \(performance.totalAttempts)
            """,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}
