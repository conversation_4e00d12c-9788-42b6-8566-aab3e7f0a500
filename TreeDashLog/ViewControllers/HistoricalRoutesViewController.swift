//
//  HistoricalRoutesViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class HistoricalRoutesViewController: UIViewController {
    
    // MARK: - Properties
    private var routes: [Route] = []
    private var filteredRoutes: [Route] = []
    private var filter = HistoricalRouteFilter()
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .insetGrouped)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .systemGroupedBackground
        table.register(RouteTableViewCell.self, forCellReuseIdentifier: RouteTableViewCell.identifier)
        table.separatorStyle = .none
        return table
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "clock.arrow.circlepath")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = "No Historical Routes"
        titleLabel.font = .systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = .systemPurple
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Complete some routes to see your training history"
        subtitleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = .secondaryLabel
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadRoutes()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadRoutes()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Historical Routes"
        view.backgroundColor = .systemGroupedBackground
        
        navigationItem.rightBarButtonItems = [
            UIBarButtonItem(
                image: UIImage(systemName: "line.3.horizontal.decrease.circle"),
                style: .plain,
                target: self,
                action: #selector(showFilterOptions)
            ),
            UIBarButtonItem(
                image: UIImage(systemName: "arrow.up.arrow.down"),
                style: .plain,
                target: self,
                action: #selector(showSortOptions)
            )
        ]
        
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(40)
        }
    }
    
    // MARK: - Data Loading
    private func loadRoutes() {
        routes = RouteManager.shared.getAllRoutes()
        applyFilters()
        updateUI()
    }
    
    private func applyFilters() {
        filteredRoutes = DashboardAnalyticsEngine.shared.filterHistoricalRoutes(with: filter)
    }
    
    private func updateUI() {
        emptyStateView.isHidden = !filteredRoutes.isEmpty
        tableView.isHidden = filteredRoutes.isEmpty
        tableView.reloadData()
        
        // Update title with count
        title = "Historical Routes (\(filteredRoutes.count))"
    }
    
    // MARK: - Actions
    @objc private func showFilterOptions() {
        let alert = UIAlertController(title: "Filter Routes", message: "Choose filter options", preferredStyle: .actionSheet)
        
        // Difficulty filter
        alert.addAction(UIAlertAction(title: "Filter by Difficulty", style: .default) { _ in
            self.showDifficultyFilter()
        })
        
        // Location filter
        alert.addAction(UIAlertAction(title: "Filter by Location", style: .default) { _ in
            self.showLocationFilter()
        })
        
        // Success rate filter
        alert.addAction(UIAlertAction(title: "Filter by Success Rate", style: .default) { _ in
            self.showSuccessRateFilter()
        })
        
        // Clear filters
        alert.addAction(UIAlertAction(title: "Clear All Filters", style: .destructive) { _ in
            self.clearFilters()
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.first
        }
        
        present(alert, animated: true)
    }
    
    @objc private func showSortOptions() {
        let alert = UIAlertController(title: "Sort Routes", message: "Choose sorting option", preferredStyle: .actionSheet)
        
        for sortOption in HistoricalRouteFilter.SortOption.allCases {
            alert.addAction(UIAlertAction(title: sortOption.displayName, style: .default) { _ in
                self.filter.sortBy = sortOption
                self.applyFilters()
                self.updateUI()
            })
        }
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.last
        }
        
        present(alert, animated: true)
    }
    
    private func showDifficultyFilter() {
        let alert = UIAlertController(title: "Filter by Difficulty", message: "Select difficulties to show", preferredStyle: .alert)
        
        for difficulty in RouteDifficulty.allCases {
            let isSelected = filter.selectedDifficulties.contains(difficulty)
            alert.addAction(UIAlertAction(title: "\(isSelected ? "✓ " : "")\(difficulty.displayName)", style: .default) { _ in
                if isSelected {
                    self.filter.selectedDifficulties.remove(difficulty)
                } else {
                    self.filter.selectedDifficulties.insert(difficulty)
                }
                self.applyFilters()
                self.updateUI()
            })
        }
        
        alert.addAction(UIAlertAction(title: "Done", style: .cancel))
        present(alert, animated: true)
    }
    
    private func showLocationFilter() {
        let locations = Set(routes.map { $0.location })
        let alert = UIAlertController(title: "Filter by Location", message: "Select locations to show", preferredStyle: .alert)
        
        for location in locations.sorted() {
            let isSelected = filter.selectedLocations.contains(location)
            alert.addAction(UIAlertAction(title: "\(isSelected ? "✓ " : "")\(location)", style: .default) { _ in
                if isSelected {
                    self.filter.selectedLocations.remove(location)
                } else {
                    self.filter.selectedLocations.insert(location)
                }
                self.applyFilters()
                self.updateUI()
            })
        }
        
        alert.addAction(UIAlertAction(title: "Done", style: .cancel))
        present(alert, animated: true)
    }
    
    private func showSuccessRateFilter() {
        let alert = UIAlertController(title: "Filter by Success Rate", message: "Set minimum success rate", preferredStyle: .alert)
        
        let successRates = [0.0, 0.25, 0.5, 0.75, 0.9]
        for rate in successRates {
            let percentage = Int(rate * 100)
            alert.addAction(UIAlertAction(title: "\(percentage)% or higher", style: .default) { _ in
                self.filter.minSuccessRate = rate
                self.applyFilters()
                self.updateUI()
            })
        }
        
        alert.addAction(UIAlertAction(title: "Clear", style: .destructive) { _ in
            self.filter.minSuccessRate = nil
            self.applyFilters()
            self.updateUI()
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        present(alert, animated: true)
    }
    
    private func clearFilters() {
        filter = HistoricalRouteFilter()
        applyFilters()
        updateUI()
    }
}

// MARK: - UITableViewDataSource
extension HistoricalRoutesViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return filteredRoutes.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: RouteTableViewCell.identifier, for: indexPath) as? RouteTableViewCell else {
            return UITableViewCell()
        }
        
        let route = filteredRoutes[indexPath.row]
        cell.configure(with: route)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension HistoricalRoutesViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 140
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let route = filteredRoutes[indexPath.row]
        let routeDetailVC = RouteDetailViewController(route: route) { [weak self] in
            self?.loadRoutes()
        }
        let navController = UINavigationController(rootViewController: routeDetailVC)
        present(navController, animated: true)
    }
    
    func tableView(_ tableView: UITableView, contextMenuConfigurationForRowAt indexPath: IndexPath, point: CGPoint) -> UIContextMenuConfiguration? {
        let route = filteredRoutes[indexPath.row]
        
        return UIContextMenuConfiguration(identifier: nil, previewProvider: nil) { _ in
            let analyzeAction = UIAction(title: "Analyze Performance", image: UIImage(systemName: "chart.bar.xaxis")) { _ in
                let result = EfficiencyAnalysisEngine.shared.analyzeRoute(route, withHistoricalData: self.routes)
                let analyticsVC = PerformanceAnalyticsViewController(analysisResult: result)
                self.navigationController?.pushViewController(analyticsVC, animated: true)
            }
            
            let shareAction = UIAction(title: "Share Route", image: UIImage(systemName: "square.and.arrow.up")) { _ in
                let shareText = """
                TreeDash Log Route: \(route.name)
                Location: \(route.location)
                Difficulty: \(route.difficulty.displayName)
                Success Rate: \(String(format: "%.1f%%", route.completionRate * 100))
                Duration: \(route.formattedDuration)
                Actions: \(route.actionNodes.count)
                """
                
                let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
                self.present(activityVC, animated: true)
            }
            
            return UIMenu(title: route.name, children: [analyzeAction, shareAction])
        }
    }
}
