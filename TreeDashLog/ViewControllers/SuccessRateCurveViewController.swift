//
//  SuccessRateCurveViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class SuccessRateCurveViewController: UIViewController {
    
    // MARK: - Properties
    private let dataPoints: [SuccessRateDataPoint]
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .systemGroupedBackground
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var chartContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.systemPurple.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        view.layer.shadowOpacity = 0.15
        return view
    }()
    
    private lazy var chartView: SuccessRateChartView = {
        let chart = SuccessRateChartView()
        chart.backgroundColor = .clear
        return chart
    }()
    
    private lazy var statsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var statsStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.distribution = .fillEqually
        stack.spacing = 1
        return stack
    }()
    
    private lazy var insightsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var insightsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .label
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - Initialization
    init(dataPoints: [SuccessRateDataPoint]) {
        self.dataPoints = dataPoints
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        configureChart()
        generateInsights()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = "Success Rate Curve"
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "square.and.arrow.up"),
            style: .plain,
            target: self,
            action: #selector(shareChart)
        )
        
        setupScrollView()
        setupConstraints()
        setupStats()
    }
    
    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        [chartContainer, statsContainer, insightsContainer].forEach {
            contentView.addSubview($0)
        }
        
        setupChartSection()
        setupStatsSection()
        setupInsightsSection()
    }
    
    private func setupChartSection() {
        let titleLabel = createSectionTitleLabel("Improvement Trajectory")
        
        [titleLabel, chartView].forEach {
            chartContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        chartView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(250)
        }
    }
    
    private func setupStatsSection() {
        let titleLabel = createSectionTitleLabel("Key Statistics")
        
        [titleLabel, statsStackView].forEach {
            statsContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(80)
        }
    }
    
    private func setupInsightsSection() {
        let titleLabel = createSectionTitleLabel("Performance Insights")
        
        [titleLabel, insightsLabel].forEach {
            insightsContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        insightsLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        chartContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        statsContainer.snp.makeConstraints { make in
            make.top.equalTo(chartContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        insightsContainer.snp.makeConstraints { make in
            make.top.equalTo(statsContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupStats() {
        guard !dataPoints.isEmpty else { return }
        
        let initialSuccessRate = dataPoints.first?.successRate ?? 0
        let finalSuccessRate = dataPoints.last?.successRate ?? 0
        let improvement = (finalSuccessRate - initialSuccessRate) * 100
        let totalAttempts = dataPoints.count
        
        let statCards = [
            createStatCard(title: "Total Attempts", value: "\(totalAttempts)", color: .systemBlue),
            createStatCard(title: "Final Success Rate", value: String(format: "%.1f%%", finalSuccessRate * 100), color: .systemGreen),
            createStatCard(title: "Improvement", value: String(format: "+%.1f%%", improvement), color: .systemPurple)
        ]
        
        statCards.forEach { statsStackView.addArrangedSubview($0) }
    }
    
    private func createStatCard(title: String, value: String, color: UIColor) -> UIView {
        let container = UIView()
        container.backgroundColor = .systemBackground
        container.layer.cornerRadius = 8
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 20, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .center
        
        [titleLabel, valueLabel].forEach { container.addSubview($0) }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(8)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.leading.trailing.bottom.equalToSuperview().inset(8)
        }
        
        return container
    }
    
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .systemPurple
        return label
    }
    
    // MARK: - Configuration
    private func configureChart() {
        chartView.configure(with: dataPoints)
    }
    
    private func generateInsights() {
        guard !dataPoints.isEmpty else {
            insightsLabel.text = "No data available for analysis."
            return
        }
        
        let initialRate = dataPoints.first?.successRate ?? 0
        let finalRate = dataPoints.last?.successRate ?? 0
        let improvement = finalRate - initialRate
        
        var insights: [String] = []
        
        if improvement > 0.3 {
            insights.append("🎉 Excellent improvement! Your success rate increased by \(String(format: "%.1f", improvement * 100))% over time.")
        } else if improvement > 0.1 {
            insights.append("📈 Good progress! You're showing steady improvement in your performance.")
        } else if improvement > 0 {
            insights.append("📊 Slight improvement detected. Consider focusing on specific weak areas.")
        } else {
            insights.append("⚠️ Performance appears to be plateauing. Time to try new training approaches.")
        }
        
        // Analyze consistency
        let successRates = dataPoints.map { $0.successRate }
        let variance = calculateVariance(successRates)
        
        if variance < 0.05 {
            insights.append("🎯 Your performance is very consistent across attempts.")
        } else if variance < 0.15 {
            insights.append("📊 Moderate consistency in performance with some variation.")
        } else {
            insights.append("🔄 High variation in performance suggests inconsistent technique or conditions.")
        }
        
        // Learning curve analysis
        if dataPoints.count >= 5 {
            let firstHalf = Array(dataPoints.prefix(dataPoints.count / 2))
            let secondHalf = Array(dataPoints.suffix(dataPoints.count / 2))
            
            let firstHalfAvg = firstHalf.map { $0.successRate }.reduce(0, +) / Double(firstHalf.count)
            let secondHalfAvg = secondHalf.map { $0.successRate }.reduce(0, +) / Double(secondHalf.count)
            
            if secondHalfAvg > firstHalfAvg + 0.1 {
                insights.append("🚀 Strong learning curve - you're getting better with practice!")
            } else if secondHalfAvg < firstHalfAvg - 0.1 {
                insights.append("😴 Performance decline in recent attempts. Consider rest or technique review.")
            }
        }
        
        insightsLabel.text = insights.joined(separator: "\n\n")
    }
    
    private func calculateVariance(_ values: [Double]) -> Double {
        guard values.count > 1 else { return 0 }

        let mean = values.reduce(0, +) / Double(values.count)
        let squaredDifferences = values.map { pow($0 - mean, 2) }
        return squaredDifferences.reduce(0, +) / Double(values.count)
    }
    
    // MARK: - Actions
    @objc private func shareChart() {
        let shareText = generateShareText()
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
        
        if let popover = activityVC.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItem
        }
        
        present(activityVC, animated: true)
    }
    
    private func generateShareText() -> String {
        guard !dataPoints.isEmpty else { return "No data available" }
        
        let initialRate = dataPoints.first?.successRate ?? 0
        let finalRate = dataPoints.last?.successRate ?? 0
        let improvement = (finalRate - initialRate) * 100
        
        return """
        TreeDash Log - Success Rate Analysis
        
        Total Attempts: \(dataPoints.count)
        Initial Success Rate: \(String(format: "%.1f%%", initialRate * 100))
        Final Success Rate: \(String(format: "%.1f%%", finalRate * 100))
        Overall Improvement: \(String(format: "+%.1f%%", improvement))
        
        Keep pushing your limits! 🏃‍♂️💪
        """
    }
}
