//
//  PerformanceAnalyticsViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class PerformanceAnalyticsViewController: UIViewController {
    
    // MARK: - Properties
    private let analysisResult: EfficiencyAnalysisResult
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .systemGroupedBackground
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // Overall Score Section
    private lazy var overallScoreContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.systemPurple.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        view.layer.shadowOpacity = 0.15
        return view
    }()
    
    private lazy var scoreLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 48, weight: .bold)
        label.textColor = .systemPurple
        label.textAlignment = .center
        return label
    }()
    
    private lazy var scoreDescriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "Overall Efficiency Score"
        label.font = .systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .label
        label.textAlignment = .center
        return label
    }()
    
    private lazy var riskLevelView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var riskLevelLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    // Metrics Grid
    private lazy var metricsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var metricsStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 1
        stack.backgroundColor = .systemGray5
        return stack
    }()
    
    // Action Performance Section
    private lazy var actionPerformanceContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var actionPerformanceTableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.register(ActionPerformanceTableViewCell.self, forCellReuseIdentifier: ActionPerformanceTableViewCell.identifier)
        table.separatorStyle = .none
        table.isScrollEnabled = false
        return table
    }()
    
    // Improvement Suggestions Section
    private lazy var suggestionsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var suggestionsTableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.register(ImprovementSuggestionTableViewCell.self, forCellReuseIdentifier: ImprovementSuggestionTableViewCell.identifier)
        table.separatorStyle = .none
        table.isScrollEnabled = false
        return table
    }()
    
    // Success Rate Curve Button
    private lazy var successCurveButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("View Success Rate Curve", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.backgroundColor = .systemPurple
        button.layer.cornerRadius = 12
        button.addTarget(self, action: #selector(showSuccessRateCurve), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    init(analysisResult: EfficiencyAnalysisResult) {
        self.analysisResult = analysisResult
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        configureWithAnalysisResult()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = "Performance Analytics"
        
        // Navigation setup
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "square.and.arrow.up"),
            style: .plain,
            target: self,
            action: #selector(shareResults)
        )
        
        setupScrollView()
        setupConstraints()
    }
    
    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        [overallScoreContainer, metricsContainer, actionPerformanceContainer, 
         suggestionsContainer, successCurveButton].forEach {
            contentView.addSubview($0)
        }
        
        setupOverallScoreSection()
        setupMetricsSection()
        setupActionPerformanceSection()
        setupSuggestionsSection()
    }
    
    private func setupOverallScoreSection() {
        riskLevelView.addSubview(riskLevelLabel)
        
        [scoreLabel, scoreDescriptionLabel, riskLevelView].forEach {
            overallScoreContainer.addSubview($0)
        }
        
        scoreLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(24)
            make.centerX.equalToSuperview()
        }
        
        scoreDescriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(scoreLabel.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
        }
        
        riskLevelView.snp.makeConstraints { make in
            make.top.equalTo(scoreDescriptionLabel.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(32)
            make.bottom.equalToSuperview().inset(24)
        }
        
        riskLevelLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(8)
        }
    }
    
    private func setupMetricsSection() {
        let titleLabel = createSectionTitleLabel("Performance Metrics")
        
        [titleLabel, metricsStackView].forEach {
            metricsContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        metricsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupActionPerformanceSection() {
        let titleLabel = createSectionTitleLabel("Action Performance")
        
        [titleLabel, actionPerformanceTableView].forEach {
            actionPerformanceContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        actionPerformanceTableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(0) // Will be updated dynamically
        }
    }
    
    private func setupSuggestionsSection() {
        let titleLabel = createSectionTitleLabel("Improvement Suggestions")
        
        [titleLabel, suggestionsTableView].forEach {
            suggestionsContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        suggestionsTableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(0) // Will be updated dynamically
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        overallScoreContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        metricsContainer.snp.makeConstraints { make in
            make.top.equalTo(overallScoreContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        actionPerformanceContainer.snp.makeConstraints { make in
            make.top.equalTo(metricsContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        suggestionsContainer.snp.makeConstraints { make in
            make.top.equalTo(actionPerformanceContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        successCurveButton.snp.makeConstraints { make in
            make.top.equalTo(suggestionsContainer.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .systemPurple
        return label
    }

    // MARK: - Configuration
    private func configureWithAnalysisResult() {
        // Overall score
        scoreLabel.text = String(format: "%.0f", analysisResult.overallMetrics.efficiencyScore)

        // Risk level
        riskLevelView.backgroundColor = analysisResult.riskAssessment.overallRisk.color
        riskLevelLabel.text = analysisResult.riskAssessment.overallRisk.displayName

        // Setup metrics
        setupMetricsData()

        // Update table heights
        updateActionPerformanceTableHeight()
        updateSuggestionsTableHeight()

        // Add entrance animations
        animateContentAppearance()
    }

    private func setupMetricsData() {
        let metrics = analysisResult.overallMetrics

        let metricRows = [
            createMetricRow(title: "Success Rate", value: String(format: "%.1f%%", metrics.successRate * 100), color: .systemGreen),
            createMetricRow(title: "Total Attempts", value: "\(metrics.totalAttempts)", color: .systemBlue),
            createMetricRow(title: "Average Time", value: formatTime(metrics.averageTime), color: .systemOrange),
            createMetricRow(title: "Best Time", value: formatTime(metrics.bestTime), color: .systemGreen),
            createMetricRow(title: "Improvement Rate", value: String(format: "%.1f%%", metrics.improvementRate), color: .systemPurple)
        ]

        metricRows.forEach { metricsStackView.addArrangedSubview($0) }
    }

    private func createMetricRow(title: String, value: String, color: UIColor) -> UIView {
        let container = UIView()
        container.backgroundColor = .systemBackground

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        titleLabel.textColor = .label

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 16, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .right

        [titleLabel, valueLabel].forEach { container.addSubview($0) }

        titleLabel.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview().inset(16)
        }

        valueLabel.snp.makeConstraints { make in
            make.trailing.centerY.equalToSuperview().inset(16)
        }

        container.snp.makeConstraints { make in
            make.height.equalTo(50)
        }

        return container
    }

    private func updateActionPerformanceTableHeight() {
        let height = CGFloat(analysisResult.actionPerformances.count * 80)
        actionPerformanceTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
    }

    private func updateSuggestionsTableHeight() {
        let height = CGFloat(analysisResult.improvementSuggestions.count * 100)
        suggestionsTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
    }

    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    private func animateContentAppearance() {
        let containers = [overallScoreContainer, metricsContainer, actionPerformanceContainer, suggestionsContainer]

        containers.enumerated().forEach { index, container in
            container.alpha = 0
            container.transform = CGAffineTransform(translationX: 0, y: 50)

            UIView.animate(withDuration: 0.6, delay: Double(index) * 0.1, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [.curveEaseOut], animations: {
                container.alpha = 1
                container.transform = .identity
            })
        }
    }

    // MARK: - Actions
    @objc private func showSuccessRateCurve() {
        let curveVC = SuccessRateCurveViewController(dataPoints: analysisResult.successRateCurve)
        navigationController?.pushViewController(curveVC, animated: true)
    }

    @objc private func shareResults() {
        let shareText = generateShareText()
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)

        if let popover = activityVC.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItem
        }

        present(activityVC, animated: true)
    }

    private func generateShareText() -> String {
        let metrics = analysisResult.overallMetrics
        return """
        TreeDash Log - Performance Analysis

        Route: \(analysisResult.routeName)
        Overall Efficiency: \(String(format: "%.0f", metrics.efficiencyScore))%
        Success Rate: \(String(format: "%.1f", metrics.successRate * 100))%
        Risk Level: \(analysisResult.riskAssessment.overallRisk.displayName)

        Total Attempts: \(metrics.totalAttempts)
        Average Time: \(formatTime(metrics.averageTime))
        Best Time: \(formatTime(metrics.bestTime))

        Top Improvement Areas:
        \(analysisResult.highPrioritySuggestions.prefix(3).map { "• \($0.title)" }.joined(separator: "\n"))
        """
    }
}

// MARK: - UITableViewDataSource
extension PerformanceAnalyticsViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if tableView == actionPerformanceTableView {
            return analysisResult.actionPerformances.count
        } else if tableView == suggestionsTableView {
            return analysisResult.improvementSuggestions.count
        }
        return 0
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView == actionPerformanceTableView {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ActionPerformanceTableViewCell.identifier, for: indexPath) as? ActionPerformanceTableViewCell else {
                return UITableViewCell()
            }

            let performance = analysisResult.actionPerformances[indexPath.row]
            cell.configure(with: performance)
            return cell
        } else if tableView == suggestionsTableView {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ImprovementSuggestionTableViewCell.identifier, for: indexPath) as? ImprovementSuggestionTableViewCell else {
                return UITableViewCell()
            }

            let suggestion = analysisResult.improvementSuggestions[indexPath.row]
            cell.configure(with: suggestion)
            return cell
        }

        return UITableViewCell()
    }
}

// MARK: - UITableViewDelegate
extension PerformanceAnalyticsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if tableView == actionPerformanceTableView {
            return 80
        } else if tableView == suggestionsTableView {
            return 100
        }
        return 44
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        if tableView == suggestionsTableView {
            let suggestion = analysisResult.improvementSuggestions[indexPath.row]
            showSuggestionDetail(suggestion)
        }
    }

    private func showSuggestionDetail(_ suggestion: ImprovementSuggestion) {
        let alert = UIAlertController(title: suggestion.title, message: suggestion.description, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Got it", style: .default))
        present(alert, animated: true)
    }
}
