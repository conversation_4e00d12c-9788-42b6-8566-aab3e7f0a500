//
//  ActionDetailViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class ActionDetailViewController: UIViewController {
    
    // MARK: - Properties
    private var actionNode: ActionNode
    private var isEditMode: Bool
    private var onSave: (ActionNode) -> Void
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .systemGroupedBackground
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // Action Info Section
    private lazy var actionInfoContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var actionNameTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "Action Name *"
        textField.borderStyle = .roundedRect
        textField.backgroundColor = .systemBackground
        return textField
    }()
    
    private lazy var actionTypeCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 8
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(ActionTypeCollectionViewCell.self, forCellWithReuseIdentifier: ActionTypeCollectionViewCell.identifier)
        collectionView.showsHorizontalScrollIndicator = false
        return collectionView
    }()
    
    // Status Section
    private lazy var statusContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var statusSegmentedControl: UISegmentedControl = {
        let items = CompletionStatus.allCases.map { $0.displayName }
        let control = UISegmentedControl(items: items)
        control.selectedSegmentIndex = 0
        control.backgroundColor = .systemBackground
        control.selectedSegmentTintColor = .systemPurple
        control.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        control.addTarget(self, action: #selector(statusChanged), for: .valueChanged)
        return control
    }()
    
    // Notes Section
    private lazy var notesContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var notesTextView: UITextView = {
        let textView = UITextView()
        textView.backgroundColor = .systemBackground
        textView.layer.cornerRadius = 8
        textView.font = .systemFont(ofSize: 16)
        textView.textColor = .label
        textView.text = "Additional notes about this action..."
        textView.textColor = .placeholderText
        textView.delegate = self
        return textView
    }()
    
    private var selectedActionType: ActionType = .jump
    
    // MARK: - Initialization
    init(actionNode: ActionNode? = nil, onSave: @escaping (ActionNode) -> Void) {
        if let existingActionNode = actionNode {
            self.actionNode = existingActionNode
            self.isEditMode = true
            self.selectedActionType = existingActionNode.type
        } else {
            self.actionNode = ActionNode(name: "", type: .jump)
            self.isEditMode = false
        }
        self.onSave = onSave
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        configureWithActionNode()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = isEditMode ? "Edit Action" : "New Action"
        
        // Navigation buttons
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveButtonTapped)
        )
        
        setupScrollView()
        setupConstraints()
    }
    
    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        [actionInfoContainer, statusContainer, notesContainer].forEach {
            contentView.addSubview($0)
        }
        
        setupActionInfoSection()
        setupStatusSection()
        setupNotesSection()
    }
    
    private func setupActionInfoSection() {
        let titleLabel = createSectionTitleLabel("Action Information")
        let typeLabel = createSubtitleLabel("Action Type")
        
        [titleLabel, actionNameTextField, typeLabel, actionTypeCollectionView].forEach {
            actionInfoContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        actionNameTextField.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
        
        typeLabel.snp.makeConstraints { make in
            make.top.equalTo(actionNameTextField.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        actionTypeCollectionView.snp.makeConstraints { make in
            make.top.equalTo(typeLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(80)
            make.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupStatusSection() {
        let titleLabel = createSectionTitleLabel("Completion Status")
        
        [titleLabel, statusSegmentedControl].forEach {
            statusContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        statusSegmentedControl.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }
    }
    
    private func setupNotesSection() {
        let titleLabel = createSectionTitleLabel("Notes")
        
        [titleLabel, notesTextView].forEach {
            notesContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        notesTextView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(100)
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        actionInfoContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        statusContainer.snp.makeConstraints { make in
            make.top.equalTo(actionInfoContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        notesContainer.snp.makeConstraints { make in
            make.top.equalTo(statusContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .systemPurple
        return label
    }
    
    private func createSubtitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }
    
    private func configureWithActionNode() {
        actionNameTextField.text = actionNode.name
        selectedActionType = actionNode.type
        statusSegmentedControl.selectedSegmentIndex = CompletionStatus.allCases.firstIndex(of: actionNode.completionStatus) ?? 0
        
        if !actionNode.notes.isEmpty {
            notesTextView.text = actionNode.notes
            notesTextView.textColor = .label
        }
        
        actionTypeCollectionView.reloadData()
        updateStatusSegmentedControlColors()
    }
    
    @objc private func statusChanged() {
        updateStatusSegmentedControlColors()
    }
    
    private func updateStatusSegmentedControlColors() {
        let selectedStatus = CompletionStatus.allCases[statusSegmentedControl.selectedSegmentIndex]
        statusSegmentedControl.selectedSegmentTintColor = selectedStatus.color
    }
    
    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }
    
    @objc private func saveButtonTapped() {
        guard validateInput() else { return }

        updateActionNodeFromUI()

        // Add a subtle success animation before saving
        UIView.animate(withDuration: 0.2, animations: {
            self.view.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.2, animations: {
                self.view.transform = .identity
            }) { _ in
                self.onSave(self.actionNode)
                self.dismiss(animated: true)
            }
        }
    }
    
    private func validateInput() -> Bool {
        guard !actionNameTextField.text!.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showAlert(title: "Missing Information", message: "Please enter an action name.")
            return false
        }
        
        return true
    }
    
    private func updateActionNodeFromUI() {
        actionNode.name = actionNameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        actionNode.type = selectedActionType
        actionNode.completionStatus = CompletionStatus.allCases[statusSegmentedControl.selectedSegmentIndex]
        
        if notesTextView.textColor != .placeholderText {
            actionNode.notes = notesTextView.text
        }
        
        actionNode.timestamp = Date()
    }
    
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UICollectionViewDataSource
extension ActionDetailViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return ActionType.allCases.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: ActionTypeCollectionViewCell.identifier, for: indexPath) as? ActionTypeCollectionViewCell else {
            return UICollectionViewCell()
        }

        let actionType = ActionType.allCases[indexPath.item]
        let isSelected = actionType == selectedActionType
        cell.configure(with: actionType, isSelected: isSelected)
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension ActionDetailViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        selectedActionType = ActionType.allCases[indexPath.item]
        collectionView.reloadData()
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension ActionDetailViewController: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 70, height: 70)
    }
}

// MARK: - UITextViewDelegate
extension ActionDetailViewController: UITextViewDelegate {
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.textColor == .placeholderText {
            textView.text = ""
            textView.textColor = .label
        }
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            textView.text = "Additional notes about this action..."
            textView.textColor = .placeholderText
        }
    }
}
