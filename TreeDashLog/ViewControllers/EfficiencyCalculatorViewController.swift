//
//  EfficiencyCalculatorViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class EfficiencyCalculatorViewController: UIViewController {
    
    // MARK: - Properties
    private var selectedRoute: Route?
    private var analysisResult: EfficiencyAnalysisResult?
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .systemGroupedBackground
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // Header Section
    private lazy var headerContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.systemPurple.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        view.layer.shadowOpacity = 0.15
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Obstacle Efficiency Calculator"
        label.font = .systemFont(ofSize: 24, weight: .bold)
        label.textColor = .systemPurple
        label.textAlignment = .center
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Analyze your performance and get improvement insights"
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    // Route Selection Section
    private lazy var routeSelectionContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var routeSelectionButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Select Route to Analyze", for: .normal)
        button.setTitleColor(.systemPurple, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .semibold)
        button.backgroundColor = .systemBackground
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 2
        button.layer.borderColor = UIColor.systemPurple.cgColor
        button.addTarget(self, action: #selector(selectRouteButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var selectedRouteLabel: UILabel = {
        let label = UILabel()
        label.text = "No route selected"
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    // Analysis Options Section
    private lazy var analysisOptionsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var includeHistoricalDataSwitch: UISwitch = {
        let toggle = UISwitch()
        toggle.isOn = true
        toggle.onTintColor = .systemPurple
        return toggle
    }()
    
    private lazy var historicalDataLabel: UILabel = {
        let label = UILabel()
        label.text = "Include Historical Data"
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .label
        return label
    }()
    
    private lazy var historicalDataDescLabel: UILabel = {
        let label = UILabel()
        label.text = "Analyze trends across multiple attempts of the same route"
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.textColor = .secondaryLabel
        label.numberOfLines = 0
        return label
    }()
    
    // Analysis Button
    private lazy var analyzeButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Analyze Performance", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .bold)
        button.backgroundColor = .systemPurple
        button.layer.cornerRadius = 12
        button.layer.shadowColor = UIColor.systemPurple.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 4)
        button.layer.shadowRadius = 8
        button.layer.shadowOpacity = 0.3
        button.addTarget(self, action: #selector(analyzeButtonTapped), for: .touchUpInside)
        button.isEnabled = false
        return button
    }()
    
    // Quick Stats Section
    private lazy var quickStatsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        view.isHidden = true
        return view
    }()
    
    private lazy var quickStatsStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.distribution = .fillEqually
        stack.spacing = 1
        return stack
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadAvailableRoutes()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadAvailableRoutes()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = "Efficiency Calculator"
        
        // Navigation setup
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "chart.bar.fill"),
            style: .plain,
            target: self,
            action: #selector(showAnalyticsHistory)
        )
        
        setupScrollView()
        setupConstraints()
        setupQuickStats()
    }
    
    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        [headerContainer, routeSelectionContainer, analysisOptionsContainer, 
         analyzeButton, quickStatsContainer].forEach {
            contentView.addSubview($0)
        }
        
        setupHeaderSection()
        setupRouteSelectionSection()
        setupAnalysisOptionsSection()
    }
    
    private func setupHeaderSection() {
        [titleLabel, subtitleLabel].forEach {
            headerContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview().inset(20)
        }
    }
    
    private func setupRouteSelectionSection() {
        let titleLabel = createSectionTitleLabel("Route Selection")
        
        [titleLabel, routeSelectionButton, selectedRouteLabel].forEach {
            routeSelectionContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        routeSelectionButton.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(50)
        }
        
        selectedRouteLabel.snp.makeConstraints { make in
            make.top.equalTo(routeSelectionButton.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupAnalysisOptionsSection() {
        let titleLabel = createSectionTitleLabel("Analysis Options")
        
        [titleLabel, includeHistoricalDataSwitch, historicalDataLabel, historicalDataDescLabel].forEach {
            analysisOptionsContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        includeHistoricalDataSwitch.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(16)
            make.trailing.equalToSuperview().inset(16)
        }
        
        historicalDataLabel.snp.makeConstraints { make in
            make.centerY.equalTo(includeHistoricalDataSwitch)
            make.leading.equalToSuperview().inset(16)
            make.trailing.equalTo(includeHistoricalDataSwitch.snp.leading).offset(-12)
        }
        
        historicalDataDescLabel.snp.makeConstraints { make in
            make.top.equalTo(historicalDataLabel.snp.bottom).offset(4)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupQuickStats() {
        quickStatsContainer.addSubview(quickStatsStackView)
        
        quickStatsStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
            make.height.equalTo(80)
        }
        
        // Add stat cards
        let statCards = [
            createStatCard(title: "Efficiency", value: "0%", color: .systemBlue),
            createStatCard(title: "Success Rate", value: "0%", color: .systemGreen),
            createStatCard(title: "Risk Level", value: "Low", color: .systemOrange)
        ]
        
        statCards.forEach { quickStatsStackView.addArrangedSubview($0) }
    }
    
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .systemPurple
        return label
    }
    
    private func createStatCard(title: String, value: String, color: UIColor) -> UIView {
        let container = UIView()
        container.backgroundColor = .systemBackground
        container.layer.cornerRadius = 8
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 20, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .center
        
        [titleLabel, valueLabel].forEach { container.addSubview($0) }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(8)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.leading.trailing.bottom.equalToSuperview().inset(8)
        }
        
        return container
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        headerContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        routeSelectionContainer.snp.makeConstraints { make in
            make.top.equalTo(headerContainer.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        analysisOptionsContainer.snp.makeConstraints { make in
            make.top.equalTo(routeSelectionContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        analyzeButton.snp.makeConstraints { make in
            make.top.equalTo(analysisOptionsContainer.snp.bottom).offset(24)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(56)
        }

        quickStatsContainer.snp.makeConstraints { make in
            make.top.equalTo(analyzeButton.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    // MARK: - Data Loading
    private func loadAvailableRoutes() {
        let routes = RouteManager.shared.getAllRoutes()
        updateUI(with: routes)
    }

    private func updateUI(with routes: [Route]) {
        if routes.isEmpty {
            routeSelectionButton.setTitle("No Routes Available - Create One First", for: .normal)
            routeSelectionButton.isEnabled = false
            analyzeButton.isEnabled = false
        } else {
            routeSelectionButton.setTitle("Select Route to Analyze", for: .normal)
            routeSelectionButton.isEnabled = true
            updateAnalyzeButtonState()
        }
    }

    private func updateAnalyzeButtonState() {
        analyzeButton.isEnabled = selectedRoute != nil

        if selectedRoute != nil {
            analyzeButton.backgroundColor = .systemPurple
            analyzeButton.layer.shadowOpacity = 0.3
        } else {
            analyzeButton.backgroundColor = .systemGray
            analyzeButton.layer.shadowOpacity = 0.1
        }
    }

    // MARK: - Actions
    @objc private func selectRouteButtonTapped() {
        let routes = RouteManager.shared.getAllRoutes()
        guard !routes.isEmpty else { return }

        let alertController = UIAlertController(title: "Select Route", message: "Choose a route to analyze", preferredStyle: .actionSheet)

        for route in routes {
            let actionTitle = "\(route.name) (\(route.routeNumber))"
            alertController.addAction(UIAlertAction(title: actionTitle, style: .default) { _ in
                self.selectedRoute = route
                self.selectedRouteLabel.text = "Selected: \(route.name)"
                self.selectedRouteLabel.textColor = .systemPurple
                self.updateAnalyzeButtonState()
            })
        }

        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alertController.popoverPresentationController {
            popover.sourceView = routeSelectionButton
            popover.sourceRect = routeSelectionButton.bounds
        }

        present(alertController, animated: true)
    }

    @objc private func analyzeButtonTapped() {
        guard let route = selectedRoute else { return }

        // Show loading state
        analyzeButton.setTitle("Analyzing...", for: .normal)
        analyzeButton.isEnabled = false

        DispatchQueue.global(qos: .userInitiated).async {
            let historicalRoutes = self.includeHistoricalDataSwitch.isOn ?
                RouteManager.shared.getAllRoutes() : []

            let result = EfficiencyAnalysisEngine.shared.analyzeRoute(route, withHistoricalData: historicalRoutes)

            DispatchQueue.main.async {
                self.analysisResult = result
                self.updateQuickStats(with: result)
                self.showAnalysisResults(result)

                // Reset button state
                self.analyzeButton.setTitle("Analyze Performance", for: .normal)
                self.analyzeButton.isEnabled = true
            }
        }
    }

    @objc private func showAnalyticsHistory() {
        let analyticsVC = AnalyticsHistoryViewController()
        let navController = UINavigationController(rootViewController: analyticsVC)
        present(navController, animated: true)
    }

    private func updateQuickStats(with result: EfficiencyAnalysisResult) {
        quickStatsContainer.isHidden = false

        // Update stat cards
        let statCards = quickStatsStackView.arrangedSubviews

        if statCards.count >= 3 {
            updateStatCard(statCards[0], value: String(format: "%.0f%%", result.overallMetrics.efficiencyScore))
            updateStatCard(statCards[1], value: String(format: "%.0f%%", result.overallMetrics.successRate * 100))
            updateStatCard(statCards[2], value: result.riskAssessment.overallRisk.displayName)
        }

        // Animate the appearance
        UIView.animate(withDuration: 0.3, delay: 0.1, options: [.curveEaseOut], animations: {
            self.quickStatsContainer.alpha = 1.0
            self.quickStatsContainer.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
        }) { _ in
            UIView.animate(withDuration: 0.2) {
                self.quickStatsContainer.transform = .identity
            }
        }
    }

    private func updateStatCard(_ card: UIView, value: String) {
        if let valueLabel = card.subviews.compactMap({ $0 as? UILabel }).last {
            valueLabel.text = value
        }
    }

    private func showAnalysisResults(_ result: EfficiencyAnalysisResult) {
        let analyticsVC = PerformanceAnalyticsViewController(analysisResult: result)
        navigationController?.pushViewController(analyticsVC, animated: true)
    }
}
