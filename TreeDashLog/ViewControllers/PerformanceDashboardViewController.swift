//
//  PerformanceDashboardViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class PerformanceDashboardViewController: UIViewController {
    
    // MARK: - Properties
    private var dashboardOverview: DashboardOverview?
    private var routeDistribution: RouteDistribution?
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .systemGroupedBackground
        scroll.showsVerticalScrollIndicator = false
        scroll.refreshControl = refreshControl
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var refreshControl: UIRefreshControl = {
        let refresh = UIRefreshControl()
        refresh.addTarget(self, action: #selector(refreshData), for: .valueChanged)
        refresh.tintColor = .systemPurple
        return refresh
    }()
    
    // Header Section
    private lazy var headerContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.systemPurple.cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        view.layer.shadowOpacity = 0.15
        return view
    }()
    
    private lazy var welcomeLabel: UILabel = {
        let label = UILabel()
        label.text = "Performance Dashboard"
        label.font = .systemFont(ofSize: 28, weight: .bold)
        label.textColor = .systemPurple
        label.textAlignment = .center
        return label
    }()
    
    private lazy var lastTrainingLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    // Quick Stats Section
    private lazy var quickStatsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var quickStatsStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .horizontal
        stack.distribution = .fillEqually
        stack.spacing = 1
        return stack
    }()
    
    // Navigation Cards Section
    private lazy var navigationContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var navigationStackView: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 12
        return stack
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadDashboardData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshData()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = "Dashboard"
        
        setupScrollView()
        setupConstraints()
        setupQuickStats()
        setupNavigationCards()
    }
    
    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        [headerContainer, quickStatsContainer, navigationContainer].forEach {
            contentView.addSubview($0)
        }
        
        setupHeaderSection()
        setupQuickStatsSection()
        setupNavigationSection()
    }
    
    private func setupHeaderSection() {
        [welcomeLabel, lastTrainingLabel].forEach {
            headerContainer.addSubview($0)
        }
        
        welcomeLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(20)
        }
        
        lastTrainingLabel.snp.makeConstraints { make in
            make.top.equalTo(welcomeLabel.snp.bottom).offset(8)
            make.leading.trailing.bottom.equalToSuperview().inset(20)
        }
    }
    
    private func setupQuickStatsSection() {
        let titleLabel = createSectionTitleLabel("Quick Stats")
        
        [titleLabel, quickStatsStackView].forEach {
            quickStatsContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        quickStatsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(80)
        }
    }
    
    private func setupNavigationSection() {
        navigationContainer.addSubview(navigationStackView)
        
        navigationStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        headerContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        quickStatsContainer.snp.makeConstraints { make in
            make.top.equalTo(headerContainer.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        navigationContainer.snp.makeConstraints { make in
            make.top.equalTo(quickStatsContainer.snp.bottom).offset(20)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupQuickStats() {
        let statCards = [
            createStatCard(title: "Total Sessions", value: "0", color: .systemBlue, icon: "figure.run"),
            createStatCard(title: "Success Rate", value: "0%", color: .systemGreen, icon: "target"),
            createStatCard(title: "Training Time", value: "0h", color: .systemOrange, icon: "clock"),
            createStatCard(title: "Current Streak", value: "0", color: .systemRed, icon: "flame")
        ]
        
        statCards.forEach { quickStatsStackView.addArrangedSubview($0) }
    }
    
    private func setupNavigationCards() {
        let navigationCards = [
            createNavigationCard(
                title: "Training Statistics",
                subtitle: "View detailed training metrics and progress",
                icon: "chart.bar.fill",
                color: .systemBlue,
                action: #selector(showTrainingStatistics)
            ),
            createNavigationCard(
                title: "Trend Analysis",
                subtitle: "Analyze success rate and timing trends",
                icon: "chart.line.uptrend.xyaxis",
                color: .systemGreen,
                action: #selector(showTrendAnalysis)
            ),
            createNavigationCard(
                title: "Failure Analysis",
                subtitle: "Identify top failure patterns and improvements",
                icon: "exclamationmark.triangle.fill",
                color: .systemOrange,
                action: #selector(showFailureAnalysis)
            ),
            createNavigationCard(
                title: "Historical Routes",
                subtitle: "Browse and filter past training sessions",
                icon: "clock.arrow.circlepath",
                color: .systemPurple,
                action: #selector(showHistoricalRoutes)
            )
        ]
        
        navigationCards.forEach { navigationStackView.addArrangedSubview($0) }
    }
    
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .systemPurple
        return label
    }
    
    private func createStatCard(title: String, value: String, color: UIColor, icon: String) -> UIView {
        let container = UIView()
        container.backgroundColor = .systemBackground
        container.layer.cornerRadius = 8
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = color
        iconImageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 18, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .center
        
        [iconImageView, titleLabel, valueLabel].forEach { container.addSubview($0) }
        
        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(8)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(4)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(2)
            make.leading.trailing.bottom.equalToSuperview().inset(4)
        }
        
        return container
    }

    private func createNavigationCard(title: String, subtitle: String, icon: String, color: UIColor, action: Selector) -> UIView {
        let container = UIView()
        container.backgroundColor = .secondarySystemGroupedBackground
        container.layer.cornerRadius = 12
        container.layer.shadowColor = UIColor.black.cgColor
        container.layer.shadowOffset = CGSize(width: 0, height: 2)
        container.layer.shadowRadius = 4
        container.layer.shadowOpacity = 0.1

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = color
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.backgroundColor = color.withAlphaComponent(0.1)
        iconImageView.layer.cornerRadius = 20
        iconImageView.layer.masksToBounds = true

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = .label

        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        subtitleLabel.textColor = .secondaryLabel
        subtitleLabel.numberOfLines = 2

        let chevronImageView = UIImageView()
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = .systemGray3
        chevronImageView.contentMode = .scaleAspectFit

        [iconImageView, titleLabel, subtitleLabel, chevronImageView].forEach {
            container.addSubview($0)
        }

        iconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(16)
            make.top.equalToSuperview().inset(16)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(16)
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.trailing.equalTo(chevronImageView.snp.leading).offset(-8)
            make.bottom.equalToSuperview().inset(16)
        }

        chevronImageView.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }

        container.snp.makeConstraints { make in
            make.height.equalTo(80)
        }

        // Add tap gesture
        let tapGesture = UITapGestureRecognizer(target: self, action: action)
        container.addGestureRecognizer(tapGesture)
        container.isUserInteractionEnabled = true

        return container
    }

    // MARK: - Data Loading
    private func loadDashboardData() {
        DispatchQueue.global(qos: .userInitiated).async {
            let overview = DashboardAnalyticsEngine.shared.generateDashboardOverview()
            let distribution = DashboardAnalyticsEngine.shared.generateRouteDistribution()

            DispatchQueue.main.async {
                self.dashboardOverview = overview
                self.routeDistribution = distribution
                self.updateUI()
            }
        }
    }

    private func updateUI() {
        guard let overview = dashboardOverview else { return }

        // Update header
        if let lastTraining = overview.lastTrainingDate {
            let formatter = RelativeDateTimeFormatter()
            formatter.unitsStyle = .full
            lastTrainingLabel.text = "Last training: \(formatter.localizedString(for: lastTraining, relativeTo: Date()))"
        } else {
            lastTrainingLabel.text = "No training sessions recorded"
        }

        // Update quick stats
        updateQuickStats(with: overview)

        // Add entrance animations
        animateContentAppearance()
    }

    private func updateQuickStats(with overview: DashboardOverview) {
        let statCards = quickStatsStackView.arrangedSubviews

        if statCards.count >= 4 {
            updateStatCard(statCards[0], value: "\(overview.totalTrainingSessions)")
            updateStatCard(statCards[1], value: overview.formattedSuccessRate)
            updateStatCard(statCards[2], value: overview.formattedTotalTime)
            updateStatCard(statCards[3], value: "\(overview.currentStreak)")
        }
    }

    private func updateStatCard(_ card: UIView, value: String) {
        if let valueLabel = card.subviews.compactMap({ $0 as? UILabel }).last {
            valueLabel.text = value
        }
    }

    private func animateContentAppearance() {
        let containers = [headerContainer, quickStatsContainer, navigationContainer]

        containers.enumerated().forEach { index, container in
            container.alpha = 0
            container.transform = CGAffineTransform(translationX: 0, y: 30)

            UIView.animate(withDuration: 0.6, delay: Double(index) * 0.1, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [.curveEaseOut], animations: {
                container.alpha = 1
                container.transform = .identity
            })
        }
    }

    // MARK: - Actions
    @objc private func refreshData() {
        loadDashboardData()

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.refreshControl.endRefreshing()
        }
    }

    @objc private func settingsButtonTapped() {
        // TODO: Implement settings
        let alert = UIAlertController(title: "Settings", message: "Dashboard settings coming soon!", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    @objc private func showTrainingStatistics() {
        let statisticsVC = TrainingStatisticsViewController()
        navigationController?.pushViewController(statisticsVC, animated: true)
    }

    @objc private func showTrendAnalysis() {
        let trendsVC = TrendAnalysisViewController()
        navigationController?.pushViewController(trendsVC, animated: true)
    }

    @objc private func showFailureAnalysis() {
        let failureVC = FailureAnalysisViewController()
        navigationController?.pushViewController(failureVC, animated: true)
    }

    @objc private func showHistoricalRoutes() {
        let historicalVC = HistoricalRoutesViewController()
        navigationController?.pushViewController(historicalVC, animated: true)
    }
}
