//
//  FailureAnalysisViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class FailureAnalysisViewController: UIViewController {
    
    // MARK: - Properties
    private var topFailures: TopFailuresAnalysis?
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .systemGroupedBackground
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // Top Failures Section
    private lazy var topFailuresContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var topFailuresTableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.register(FailureAnalysisTableViewCell.self, forCellReuseIdentifier: FailureAnalysisTableViewCell.identifier)
        table.separatorStyle = .none
        table.isScrollEnabled = false
        return table
    }()
    
    // Improvement Opportunities Section
    private lazy var improvementContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var improvementTableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.register(ImprovementOpportunityTableViewCell.self, forCellReuseIdentifier: ImprovementOpportunityTableViewCell.identifier)
        table.separatorStyle = .none
        table.isScrollEnabled = false
        return table
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadFailureData()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = "Failure Analysis"
        
        setupScrollView()
        setupConstraints()
    }
    
    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        [topFailuresContainer, improvementContainer].forEach {
            contentView.addSubview($0)
        }
        
        setupTopFailuresSection()
        setupImprovementSection()
    }
    
    private func setupTopFailuresSection() {
        let titleLabel = createSectionTitleLabel("Top 5 Failed Actions")
        
        [titleLabel, topFailuresTableView].forEach {
            topFailuresContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        topFailuresTableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(0) // Will be updated dynamically
        }
    }
    
    private func setupImprovementSection() {
        let titleLabel = createSectionTitleLabel("Improvement Opportunities")
        
        [titleLabel, improvementTableView].forEach {
            improvementContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        improvementTableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(0) // Will be updated dynamically
        }
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        topFailuresContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }
        
        improvementContainer.snp.makeConstraints { make in
            make.top.equalTo(topFailuresContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .systemPurple
        return label
    }
    
    // MARK: - Data Loading
    private func loadFailureData() {
        DispatchQueue.global(qos: .userInitiated).async {
            let failures = DashboardAnalyticsEngine.shared.generateTopFailuresAnalysis()
            
            DispatchQueue.main.async {
                self.topFailures = failures
                self.updateUI()
            }
        }
    }
    
    private func updateUI() {
        guard let failures = topFailures else { return }
        
        updateTopFailuresTableHeight(failures.topFailedActions.count)
        updateImprovementTableHeight(failures.improvementOpportunities.count)
        
        // Animate appearance
        animateContentAppearance()
    }
    
    private func updateTopFailuresTableHeight(_ count: Int) {
        let height = CGFloat(count * 80)
        topFailuresTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
        topFailuresTableView.reloadData()
    }
    
    private func updateImprovementTableHeight(_ count: Int) {
        let height = CGFloat(count * 100)
        improvementTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
        improvementTableView.reloadData()
    }
    
    private func animateContentAppearance() {
        let containers = [topFailuresContainer, improvementContainer]
        
        containers.enumerated().forEach { index, container in
            container.alpha = 0
            container.transform = CGAffineTransform(translationX: 0, y: 30)
            
            UIView.animate(withDuration: 0.6, delay: Double(index) * 0.1, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: [.curveEaseOut], animations: {
                container.alpha = 1
                container.transform = .identity
            })
        }
    }
}

// MARK: - UITableViewDataSource
extension FailureAnalysisViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let failures = topFailures else { return 0 }
        
        if tableView == topFailuresTableView {
            return failures.topFailedActions.count
        } else if tableView == improvementTableView {
            return failures.improvementOpportunities.count
        }
        return 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let failures = topFailures else { return UITableViewCell() }
        
        if tableView == topFailuresTableView {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: FailureAnalysisTableViewCell.identifier, for: indexPath) as? FailureAnalysisTableViewCell else {
                return UITableViewCell()
            }
            
            let failure = failures.topFailedActions[indexPath.row]
            cell.configure(with: failure)
            return cell
        } else if tableView == improvementTableView {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: ImprovementOpportunityTableViewCell.identifier, for: indexPath) as? ImprovementOpportunityTableViewCell else {
                return UITableViewCell()
            }
            
            let opportunity = failures.improvementOpportunities[indexPath.row]
            cell.configure(with: opportunity)
            return cell
        }
        
        return UITableViewCell()
    }
}

// MARK: - UITableViewDelegate
extension FailureAnalysisViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if tableView == topFailuresTableView {
            return 80
        } else if tableView == improvementTableView {
            return 100
        }
        return 44
    }
}

// MARK: - Failure Analysis Cell
class FailureAnalysisTableViewCell: UITableViewCell {
    static let identifier = "FailureAnalysisTableViewCell"
    
    private let actionIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemRed
        imageView.backgroundColor = .systemRed.withAlphaComponent(0.1)
        imageView.layer.cornerRadius = 16
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    private let actionNameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = .label
        return label
    }()
    
    private let failureRateLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .semibold)
        label.textColor = .systemRed
        return label
    }()
    
    private let failureCountLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        [actionIconImageView, actionNameLabel, failureRateLabel, failureCountLabel].forEach {
            contentView.addSubview($0)
        }
        
        actionIconImageView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        actionNameLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalToSuperview().inset(12)
            make.trailing.equalTo(failureRateLabel.snp.leading).offset(-8)
        }
        
        failureCountLabel.snp.makeConstraints { make in
            make.leading.equalTo(actionIconImageView.snp.trailing).offset(12)
            make.top.equalTo(actionNameLabel.snp.bottom).offset(4)
            make.bottom.equalToSuperview().inset(12)
        }
        
        failureRateLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
        }
    }
    
    func configure(with failure: FailureAnalysis) {
        actionIconImageView.image = UIImage(systemName: failure.actionType.systemIcon)
        actionNameLabel.text = failure.actionType.displayName
        failureRateLabel.text = failure.formattedFailureRate
        failureCountLabel.text = "\(failure.failureCount) failures"
    }
}

// MARK: - Improvement Opportunity Cell
class ImprovementOpportunityTableViewCell: UITableViewCell {
    static let identifier = "ImprovementOpportunityTableViewCell"
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.textColor = .label
        label.numberOfLines = 2
        return label
    }()
    
    private let descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryLabel
        label.numberOfLines = 2
        return label
    }()
    
    private let impactLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .semibold)
        label.textColor = .systemGreen
        return label
    }()
    
    private let priorityView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 8
        return view
    }()
    
    private let priorityLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 10, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        priorityView.addSubview(priorityLabel)
        
        [titleLabel, descriptionLabel, impactLabel, priorityView].forEach {
            contentView.addSubview($0)
        }
        
        priorityView.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview().inset(12)
            make.width.equalTo(60)
            make.height.equalTo(20)
        }
        
        priorityLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(2)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().inset(12)
            make.trailing.equalTo(priorityView.snp.leading).offset(-8)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.leading.trailing.equalToSuperview().inset(12)
        }
        
        impactLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(4)
            make.leading.trailing.bottom.equalToSuperview().inset(12)
        }
    }
    
    func configure(with opportunity: ImprovementOpportunity) {
        titleLabel.text = opportunity.title
        descriptionLabel.text = opportunity.description
        impactLabel.text = "Impact: \(opportunity.potentialImpact)"
        
        priorityView.backgroundColor = opportunity.priority.color
        priorityLabel.text = opportunity.priority.displayName.prefix(4).uppercased()
    }
}
