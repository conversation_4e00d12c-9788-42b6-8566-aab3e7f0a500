//
//  RouteDetailViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class RouteDetailViewController: UIViewController {
    
    // MARK: - Properties
    private var route: Route
    private var isEditMode: Bool
    private var isTimerRunning = false
    private var startTime: Date?
    private var timer: Timer?
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scroll = UIScrollView()
        scroll.backgroundColor = .systemGroupedBackground
        scroll.showsVerticalScrollIndicator = false
        return scroll
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    // Basic Info Section
    private lazy var basicInfoContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var routeNumberTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "Route Number (auto-generated)"
        textField.borderStyle = .roundedRect
        textField.backgroundColor = .systemBackground
        return textField
    }()
    
    private lazy var routeNameTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "Route Name *"
        textField.borderStyle = .roundedRect
        textField.backgroundColor = .systemBackground
        return textField
    }()
    
    private lazy var locationTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "Location *"
        textField.borderStyle = .roundedRect
        textField.backgroundColor = .systemBackground
        return textField
    }()
    
    private lazy var startPointTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "Start Point"
        textField.borderStyle = .roundedRect
        textField.backgroundColor = .systemBackground
        return textField
    }()
    
    private lazy var endPointTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "End Point"
        textField.borderStyle = .roundedRect
        textField.backgroundColor = .systemBackground
        return textField
    }()
    
    // Difficulty Section
    private lazy var difficultyContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var difficultySegmentedControl: UISegmentedControl = {
        let items = RouteDifficulty.allCases.map { $0.displayName }
        let control = UISegmentedControl(items: items)
        control.selectedSegmentIndex = 0
        control.backgroundColor = .systemBackground
        control.selectedSegmentTintColor = .systemPurple
        control.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        return control
    }()
    
    // Timer Section
    private lazy var timerContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var timerLabel: UILabel = {
        let label = UILabel()
        label.text = "00:00"
        label.font = .monospacedDigitSystemFont(ofSize: 32, weight: .bold)
        label.textColor = .systemPurple
        label.textAlignment = .center
        return label
    }()
    
    private lazy var timerButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Start Timer", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemGreen
        button.layer.cornerRadius = 8
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.addTarget(self, action: #selector(timerButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // Actions Section
    private lazy var actionsContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var actionsTableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .clear
        table.register(ActionNodeTableViewCell.self, forCellReuseIdentifier: ActionNodeTableViewCell.identifier)
        table.separatorStyle = .none
        table.isScrollEnabled = false
        return table
    }()
    
    private lazy var addActionButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Add Action", for: .normal)
        button.setImage(UIImage(systemName: "plus.circle.fill"), for: .normal)
        button.setTitleColor(.systemPurple, for: .normal)
        button.tintColor = .systemPurple
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.addTarget(self, action: #selector(addActionButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // Notes Section
    private lazy var notesContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private lazy var notesTextView: UITextView = {
        let textView = UITextView()
        textView.backgroundColor = .systemBackground
        textView.layer.cornerRadius = 8
        textView.font = .systemFont(ofSize: 16)
        textView.textColor = .label
        textView.text = "Failure notes and observations..."
        textView.textColor = .placeholderText
        textView.delegate = self
        return textView
    }()
    
    // MARK: - Initialization
    init(route: Route? = nil) {
        if let existingRoute = route {
            self.route = existingRoute
            self.isEditMode = true
        } else {
            self.route = Route(name: "", location: "")
            self.isEditMode = false
        }
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        configureWithRoute()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopTimer()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = isEditMode ? "Edit Route" : "New Route"
        
        // Navigation buttons
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveButtonTapped)
        )
        
        setupScrollView()
        setupConstraints()
    }
    
    private func setupScrollView() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        [basicInfoContainer, difficultyContainer, timerContainer, 
         actionsContainer, notesContainer].forEach {
            contentView.addSubview($0)
        }
        
        setupBasicInfoSection()
        setupDifficultySection()
        setupTimerSection()
        setupActionsSection()
        setupNotesSection()
    }
    
    private func setupBasicInfoSection() {
        let titleLabel = createSectionTitleLabel("Basic Information")
        
        [titleLabel, routeNumberTextField, routeNameTextField, 
         locationTextField, startPointTextField, endPointTextField].forEach {
            basicInfoContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        routeNumberTextField.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
        
        routeNameTextField.snp.makeConstraints { make in
            make.top.equalTo(routeNumberTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
        
        locationTextField.snp.makeConstraints { make in
            make.top.equalTo(routeNameTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
        
        startPointTextField.snp.makeConstraints { make in
            make.top.equalTo(locationTextField.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
        
        endPointTextField.snp.makeConstraints { make in
            make.top.equalTo(startPointTextField.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
    }
    
    private func setupDifficultySection() {
        let titleLabel = createSectionTitleLabel("Difficulty Level")
        
        [titleLabel, difficultySegmentedControl].forEach {
            difficultyContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        difficultySegmentedControl.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }
    }
    
    private func setupTimerSection() {
        let titleLabel = createSectionTitleLabel("Training Timer")
        
        [titleLabel, timerLabel, timerButton].forEach {
            timerContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        timerLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.centerX.equalToSuperview()
        }
        
        timerButton.snp.makeConstraints { make in
            make.top.equalTo(timerLabel.snp.bottom).offset(12)
            make.centerX.equalToSuperview()
            make.width.equalTo(120)
            make.height.equalTo(44)
            make.bottom.equalToSuperview().inset(16)
        }
    }
    
    private func setupActionsSection() {
        let titleLabel = createSectionTitleLabel("Action Nodes")
        
        [titleLabel, actionsTableView, addActionButton].forEach {
            actionsContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        actionsTableView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.equalToSuperview().inset(16)
            make.height.equalTo(0) // Will be updated dynamically
        }
        
        addActionButton.snp.makeConstraints { make in
            make.top.equalTo(actionsTableView.snp.bottom).offset(12)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
    }
    
    private func setupNotesSection() {
        let titleLabel = createSectionTitleLabel("Failure Notes")
        
        [titleLabel, notesTextView].forEach {
            notesContainer.addSubview($0)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(16)
        }
        
        notesTextView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.leading.trailing.bottom.equalToSuperview().inset(16)
            make.height.equalTo(100)
        }
    }
    
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .systemPurple
        return label
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        basicInfoContainer.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        difficultyContainer.snp.makeConstraints { make in
            make.top.equalTo(basicInfoContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        timerContainer.snp.makeConstraints { make in
            make.top.equalTo(difficultyContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        actionsContainer.snp.makeConstraints { make in
            make.top.equalTo(timerContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
        }

        notesContainer.snp.makeConstraints { make in
            make.top.equalTo(actionsContainer.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }

    private func configureWithRoute() {
        routeNumberTextField.text = route.routeNumber
        routeNameTextField.text = route.name
        locationTextField.text = route.location
        startPointTextField.text = route.startPoint
        endPointTextField.text = route.endPoint
        difficultySegmentedControl.selectedSegmentIndex = route.difficulty.rawValue - 1

        if !route.failureNotes.isEmpty {
            notesTextView.text = route.failureNotes
            notesTextView.textColor = .label
        }

        updateTimerDisplay()
        updateActionsTableHeight()
    }

    private func updateTimerDisplay() {
        if isTimerRunning, let startTime = startTime {
            let elapsed = Date().timeIntervalSince(startTime) + route.totalDuration
            timerLabel.text = formatTime(elapsed)
        } else {
            timerLabel.text = formatTime(route.totalDuration)
        }
    }

    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }

    private func updateActionsTableHeight() {
        let height = CGFloat(route.actionNodes.count * 60) // 60 points per cell
        actionsTableView.snp.updateConstraints { make in
            make.height.equalTo(height)
        }
        actionsTableView.reloadData()
    }

    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        stopTimer()
        dismiss(animated: true)
    }

    @objc private func saveButtonTapped() {
        guard validateInput() else { return }

        stopTimer()
        updateRouteFromUI()
        RouteManager.shared.saveRoute(route)
        dismiss(animated: true)
    }

    @objc private func timerButtonTapped() {
        if isTimerRunning {
            stopTimer()
        } else {
            startTimer()
        }
    }

    @objc private func addActionButtonTapped() {
        let actionDetailVC = ActionDetailViewController { [weak self] actionNode in
            self?.route.addActionNode(actionNode)
            self?.updateActionsTableHeight()
        }
        let navController = UINavigationController(rootViewController: actionDetailVC)
        present(navController, animated: true)
    }

    private func startTimer() {
        isTimerRunning = true
        startTime = Date()
        timerButton.setTitle("Stop Timer", for: .normal)
        timerButton.backgroundColor = .systemRed

        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateTimerDisplay()
        }
    }

    private func stopTimer() {
        guard isTimerRunning else { return }

        isTimerRunning = false
        timer?.invalidate()
        timer = nil

        if let startTime = startTime {
            route.totalDuration += Date().timeIntervalSince(startTime)
        }

        timerButton.setTitle("Start Timer", for: .normal)
        timerButton.backgroundColor = .systemGreen
        updateTimerDisplay()
    }

    private func validateInput() -> Bool {
        guard !routeNameTextField.text!.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showAlert(title: "Missing Information", message: "Please enter a route name.")
            return false
        }

        guard !locationTextField.text!.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showAlert(title: "Missing Information", message: "Please enter a location.")
            return false
        }

        return true
    }

    private func updateRouteFromUI() {
        route.routeNumber = routeNumberTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? route.routeNumber
        route.name = routeNameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        route.location = locationTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        route.startPoint = startPointTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        route.endPoint = endPointTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        route.difficulty = RouteDifficulty(rawValue: difficultySegmentedControl.selectedSegmentIndex + 1) ?? .beginner

        if notesTextView.textColor != .placeholderText {
            route.failureNotes = notesTextView.text
        }

        route.lastModified = Date()
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension RouteDetailViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return route.actionNodes.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: ActionNodeTableViewCell.identifier, for: indexPath) as? ActionNodeTableViewCell else {
            return UITableViewCell()
        }

        let actionNode = route.actionNodes[indexPath.row]
        cell.configure(with: actionNode)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension RouteDetailViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 60
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let actionNode = route.actionNodes[indexPath.row]
        let actionDetailVC = ActionDetailViewController(actionNode: actionNode) { [weak self] updatedActionNode in
            self?.route.updateActionNode(at: indexPath.row, with: updatedActionNode)
            self?.updateActionsTableHeight()
        }
        let navController = UINavigationController(rootViewController: actionDetailVC)
        present(navController, animated: true)
    }

    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            route.removeActionNode(at: indexPath.row)
            updateActionsTableHeight()
        }
    }
}

// MARK: - UITextViewDelegate
extension RouteDetailViewController: UITextViewDelegate {
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.textColor == .placeholderText {
            textView.text = ""
            textView.textColor = .label
        }
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            textView.text = "Failure notes and observations..."
            textView.textColor = .placeholderText
        }
    }
}
