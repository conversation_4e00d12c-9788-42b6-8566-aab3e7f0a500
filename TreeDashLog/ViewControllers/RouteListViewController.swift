//
//  RouteListViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class RouteListViewController: UIViewController {
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .insetGrouped)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .systemGroupedBackground
        table.register(RouteTableViewCell.self, forCellReuseIdentifier: RouteTableViewCell.identifier)
        table.separatorStyle = .none
        return table
    }()
    
    private lazy var searchController: UISearchController = {
        let search = UISearchController(searchResultsController: nil)
        search.searchResultsUpdater = self
        search.obscuresBackgroundDuringPresentation = false
        search.searchBar.placeholder = "Search routes..."
        search.searchBar.tintColor = .systemPurple
        return search
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "figure.run.circle")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = "No Routes Yet"
        titleLabel.font = .systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = .systemPurple
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Start tracking your parkour adventures by adding your first route"
        subtitleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = .secondaryLabel
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - Properties
    private var routes: [Route] = []
    private var filteredRoutes: [Route] = []
    private var isSearching: Bool {
        return searchController.isActive && !searchController.searchBar.text!.isEmpty
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadRoutes()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadRoutes()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "TreeDash Log"
        view.backgroundColor = .systemGroupedBackground
        
        // Navigation setup
        navigationItem.searchController = searchController
        navigationItem.hidesSearchBarWhenScrolling = false
        
        let addButton = UIBarButtonItem(
            image: UIImage(systemName: "plus"),
            style: .plain,
            target: self,
            action: #selector(addRouteButtonTapped)
        )
        navigationItem.rightBarButtonItem = addButton
        
        let filterButton = UIBarButtonItem(
            image: UIImage(systemName: "line.3.horizontal.decrease.circle"),
            style: .plain,
            target: self,
            action: #selector(filterButtonTapped)
        )
        navigationItem.leftBarButtonItem = filterButton
        
        // Add table view
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        // Add empty state view
        view.addSubview(emptyStateView)
        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(40)
        }
    }
    
    private func loadRoutes() {
        routes = RouteManager.shared.getAllRoutes()
        updateUI()
    }
    
    private func updateUI() {
        let routesToShow = isSearching ? filteredRoutes : routes
        emptyStateView.isHidden = !routesToShow.isEmpty
        tableView.isHidden = routesToShow.isEmpty
        tableView.reloadData()
    }
    
    // MARK: - Actions
    @objc private func addRouteButtonTapped() {
        let routeDetailVC = RouteDetailViewController { [weak self] in
            self?.loadRoutes()
            self?.animateNewRouteAdded()
        }
        let navController = UINavigationController(rootViewController: routeDetailVC)
        present(navController, animated: true)
    }

    private func animateNewRouteAdded() {
        // Add a subtle animation when a new route is added
        UIView.transition(with: tableView, duration: 0.3, options: .transitionCrossDissolve, animations: {
            self.tableView.reloadData()
        }, completion: nil)
    }
    
    @objc private func filterButtonTapped() {
        let alertController = UIAlertController(title: "Filter Routes", message: "Choose a filter option", preferredStyle: .actionSheet)
        
        // All routes
        alertController.addAction(UIAlertAction(title: "All Routes", style: .default) { _ in
            self.routes = RouteManager.shared.getAllRoutes()
            self.updateUI()
        })
        
        // Filter by difficulty
        for difficulty in RouteDifficulty.allCases {
            alertController.addAction(UIAlertAction(title: difficulty.displayName, style: .default) { _ in
                self.routes = RouteManager.shared.getRoutesByDifficulty(difficulty)
                self.updateUI()
            })
        }
        
        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alertController.popoverPresentationController {
            popover.barButtonItem = navigationItem.leftBarButtonItem
        }
        
        present(alertController, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension RouteListViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return isSearching ? filteredRoutes.count : routes.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: RouteTableViewCell.identifier, for: indexPath) as? RouteTableViewCell else {
            return UITableViewCell()
        }
        
        let route = isSearching ? filteredRoutes[indexPath.row] : routes[indexPath.row]
        cell.configure(with: route)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension RouteListViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let route = isSearching ? filteredRoutes[indexPath.row] : routes[indexPath.row]
        let routeDetailVC = RouteDetailViewController(route: route) { [weak self] in
            self?.loadRoutes()
        }
        let navController = UINavigationController(rootViewController: routeDetailVC)
        present(navController, animated: true)
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 180
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            let route = isSearching ? filteredRoutes[indexPath.row] : routes[indexPath.row]
            
            let alert = UIAlertController(title: "Delete Route", message: "Are you sure you want to delete this route?", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            alert.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
                RouteManager.shared.deleteRoute(withId: route.id)
                self.loadRoutes()
            })
            
            present(alert, animated: true)
        }
    }
}

// MARK: - UISearchResultsUpdating
extension RouteListViewController: UISearchResultsUpdating {
    func updateSearchResults(for searchController: UISearchController) {
        guard let searchText = searchController.searchBar.text else { return }
        filteredRoutes = RouteManager.shared.searchRoutes(query: searchText)
        updateUI()
    }
}
