//
//  AnalyticsHistoryViewController.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit
import SnapKit

class AnalyticsHistoryViewController: UIViewController {
    
    // MARK: - Properties
    private var analysisHistory: [EfficiencyAnalysisResult] = []
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .insetGrouped)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = .systemGroupedBackground
        table.register(AnalysisHistoryTableViewCell.self, forCellReuseIdentifier: AnalysisHistoryTableViewCell.identifier)
        table.separatorStyle = .none
        return table
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chart.bar.xaxis")
        imageView.tintColor = .systemPurple
        imageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = "No Analysis History"
        titleLabel.font = .systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = .systemPurple
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Run your first efficiency analysis to see results here"
        subtitleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = .secondaryLabel
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.leading.trailing.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.leading.trailing.equalToSuperview().inset(20)
            make.bottom.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadAnalysisHistory()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Analysis History"
        view.backgroundColor = .systemGroupedBackground
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .close,
            target: self,
            action: #selector(closeButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "trash"),
            style: .plain,
            target: self,
            action: #selector(clearHistoryButtonTapped)
        )
        
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(40)
        }
    }
    
    private func loadAnalysisHistory() {
        // In a real app, this would load from persistent storage
        // For now, we'll show empty state
        analysisHistory = []
        updateUI()
    }
    
    private func updateUI() {
        emptyStateView.isHidden = !analysisHistory.isEmpty
        tableView.isHidden = analysisHistory.isEmpty
        navigationItem.rightBarButtonItem?.isEnabled = !analysisHistory.isEmpty
        tableView.reloadData()
    }
    
    // MARK: - Actions
    @objc private func closeButtonTapped() {
        dismiss(animated: true)
    }
    
    @objc private func clearHistoryButtonTapped() {
        let alert = UIAlertController(title: "Clear History", message: "Are you sure you want to delete all analysis history?", preferredStyle: .alert)
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Clear", style: .destructive) { _ in
            self.analysisHistory.removeAll()
            self.updateUI()
        })
        
        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension AnalyticsHistoryViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return analysisHistory.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: AnalysisHistoryTableViewCell.identifier, for: indexPath) as? AnalysisHistoryTableViewCell else {
            return UITableViewCell()
        }
        
        let analysis = analysisHistory[indexPath.row]
        cell.configure(with: analysis)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension AnalyticsHistoryViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 100
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let analysis = analysisHistory[indexPath.row]
        let analyticsVC = PerformanceAnalyticsViewController(analysisResult: analysis)
        navigationController?.pushViewController(analyticsVC, animated: true)
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            analysisHistory.remove(at: indexPath.row)
            tableView.deleteRows(at: [indexPath], with: .fade)
            updateUI()
        }
    }
}

// MARK: - Analysis History Cell
class AnalysisHistoryTableViewCell: UITableViewCell {
    static let identifier = "AnalysisHistoryTableViewCell"
    
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        return view
    }()
    
    private let routeNameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .label
        return label
    }()
    
    private let dateLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private let efficiencyScoreLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 20, weight: .bold)
        label.textColor = .systemPurple
        return label
    }()
    
    private let riskLevelView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 8
        return view
    }()
    
    private let riskLevelLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        riskLevelView.addSubview(riskLevelLabel)
        
        [routeNameLabel, dateLabel, efficiencyScoreLabel, riskLevelView].forEach {
            containerView.addSubview($0)
        }
        
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        
        routeNameLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().inset(16)
            make.trailing.equalTo(efficiencyScoreLabel.snp.leading).offset(-8)
        }
        
        dateLabel.snp.makeConstraints { make in
            make.top.equalTo(routeNameLabel.snp.bottom).offset(4)
            make.leading.equalToSuperview().inset(16)
            make.trailing.equalTo(efficiencyScoreLabel.snp.leading).offset(-8)
        }
        
        riskLevelView.snp.makeConstraints { make in
            make.top.equalTo(dateLabel.snp.bottom).offset(8)
            make.leading.bottom.equalToSuperview().inset(16)
            make.width.equalTo(80)
            make.height.equalTo(24)
        }
        
        riskLevelLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(4)
        }
        
        efficiencyScoreLabel.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(16)
            make.centerY.equalToSuperview()
        }
    }
    
    func configure(with analysis: EfficiencyAnalysisResult) {
        routeNameLabel.text = analysis.routeName
        
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        dateLabel.text = formatter.string(from: analysis.analysisDate)
        
        efficiencyScoreLabel.text = String(format: "%.0f%%", analysis.overallMetrics.efficiencyScore)
        
        riskLevelView.backgroundColor = analysis.riskAssessment.overallRisk.color
        riskLevelLabel.text = analysis.riskAssessment.overallRisk.displayName
    }
}
