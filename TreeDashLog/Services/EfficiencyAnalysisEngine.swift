//
//  EfficiencyAnalysisEngine.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import Foundation

class EfficiencyAnalysisEngine {
    static let shared = EfficiencyAnalysisEngine()
    
    private init() {}
    
    // MARK: - Main Analysis Function
    func analyzeRoute(_ route: Route, withHistoricalData historicalRoutes: [Route] = []) -> EfficiencyAnalysisResult {
        let allRoutes = [route] + historicalRoutes.filter { $0.id != route.id }
        let routeAttempts = allRoutes.filter { $0.name == route.name || $0.routeNumber == route.routeNumber }
        
        let overallMetrics = calculateOverallMetrics(for: routeAttempts)
        let actionPerformances = analyzeActionPerformances(for: routeAttempts)
        let improvementSuggestions = generateImprovementSuggestions(from: actionPerformances)
        let successRateCurve = calculateSuccessRateCurve(for: routeAttempts)
        let timeDistribution = calculateTimeDistribution(for: routeAttempts)
        let riskAssessment = assessRisks(from: actionPerformances)
        
        return EfficiencyAnalysisResult(
            routeId: route.id,
            routeName: route.name,
            analysisDate: Date(),
            overallMetrics: overallMetrics,
            actionPerformances: actionPerformances,
            improvementSuggestions: improvementSuggestions,
            successRateCurve: successRateCurve,
            timeDistribution: timeDistribution,
            riskAssessment: riskAssessment
        )
    }
    
    // MARK: - Overall Metrics Calculation
    private func calculateOverallMetrics(for routes: [Route]) -> PerformanceMetrics {
        guard !routes.isEmpty else {
            return PerformanceMetrics(
                routeId: UUID(),
                totalAttempts: 0,
                successfulAttempts: 0,
                totalTime: 0,
                averageTime: 0,
                bestTime: 0,
                worstTime: 0,
                efficiencyScore: 0,
                improvementRate: 0,
                riskLevel: .critical
            )
        }
        
        let totalAttempts = routes.count
        let successfulAttempts = routes.filter { $0.completionRate >= 0.8 }.count
        let times = routes.map { $0.totalDuration }.filter { $0 > 0 }
        
        let totalTime = times.reduce(0, +)
        let averageTime = times.isEmpty ? 0 : totalTime / Double(times.count)
        let bestTime = times.min() ?? 0
        let worstTime = times.max() ?? 0
        
        let efficiencyScore = calculateEfficiencyScore(
            successRate: Double(successfulAttempts) / Double(totalAttempts),
            averageTime: averageTime,
            consistency: calculateTimeConsistency(times: times)
        )
        
        let improvementRate = calculateImprovementRate(for: routes)
        let riskLevel = determineRiskLevel(from: routes)
        
        return PerformanceMetrics(
            routeId: routes.first?.id ?? UUID(),
            totalAttempts: totalAttempts,
            successfulAttempts: successfulAttempts,
            totalTime: totalTime,
            averageTime: averageTime,
            bestTime: bestTime,
            worstTime: worstTime,
            efficiencyScore: efficiencyScore,
            improvementRate: improvementRate,
            riskLevel: riskLevel
        )
    }
    
    // MARK: - Action Performance Analysis
    private func analyzeActionPerformances(for routes: [Route]) -> [ActionPerformance] {
        var actionStats: [ActionType: (attempts: Int, successes: Int, totalTime: TimeInterval, times: [TimeInterval])] = [:]
        
        for route in routes {
            for action in route.actionNodes {
                let key = action.type
                let isSuccess = action.completionStatus == .success
                let time = action.timestamp.timeIntervalSince(route.createdDate)
                
                if actionStats[key] == nil {
                    actionStats[key] = (0, 0, 0, [])
                }
                
                actionStats[key]!.attempts += 1
                if isSuccess {
                    actionStats[key]!.successes += 1
                }
                actionStats[key]!.totalTime += time
                actionStats[key]!.times.append(time)
            }
        }
        
        return actionStats.map { (actionType, stats) in
            let averageTime = stats.attempts > 0 ? stats.totalTime / Double(stats.attempts) : 0
            let failureReasons = generateFailureReasons(for: actionType, successRate: Double(stats.successes) / Double(stats.attempts))
            let suggestions = generateActionSuggestions(for: actionType, stats: stats)
            
            return ActionPerformance(
                actionType: actionType,
                actionName: actionType.displayName,
                attempts: stats.attempts,
                successes: stats.successes,
                failures: stats.attempts - stats.successes,
                averageTime: averageTime,
                totalTime: stats.totalTime,
                failureReasons: failureReasons,
                improvementSuggestions: suggestions
            )
        }.sorted { $0.failureRate > $1.failureRate }
    }
    
    // MARK: - Helper Calculations
    private func calculateEfficiencyScore(successRate: Double, averageTime: TimeInterval, consistency: Double) -> Double {
        let timeScore = max(0, 1.0 - (averageTime / 120.0)) // Normalize against 2 minutes
        let efficiencyScore = (successRate * 0.5) + (timeScore * 0.3) + (consistency * 0.2)
        return min(1.0, max(0.0, efficiencyScore)) * 100
    }
    
    private func calculateTimeConsistency(times: [TimeInterval]) -> Double {
        guard times.count > 1 else { return 1.0 }

        let average = times.reduce(0, +) / Double(times.count)
        guard average > 0 else { return 1.0 } // Prevent division by zero

        let variance = times.map { pow($0 - average, 2) }.reduce(0, +) / Double(times.count)
        let standardDeviation = sqrt(variance)

        return max(0, 1.0 - (standardDeviation / average))
    }
    
    private func calculateImprovementRate(for routes: [Route]) -> Double {
        guard routes.count >= 2 else { return 0.0 }

        let sortedRoutes = routes.sorted { $0.createdDate < $1.createdDate }
        let firstHalf = Array(sortedRoutes.prefix(sortedRoutes.count / 2))
        let secondHalf = Array(sortedRoutes.suffix(sortedRoutes.count / 2))

        guard !firstHalf.isEmpty && !secondHalf.isEmpty else { return 0.0 }

        let firstHalfAverage = firstHalf.map { $0.completionRate }.reduce(0, +) / Double(firstHalf.count)
        let secondHalfAverage = secondHalf.map { $0.completionRate }.reduce(0, +) / Double(secondHalf.count)

        guard firstHalfAverage > 0 else { return 0.0 } // Prevent division by zero

        return ((secondHalfAverage - firstHalfAverage) / firstHalfAverage) * 100
    }
    
    private func determineRiskLevel(from routes: [Route]) -> RiskLevel {
        let averageCompletionRate = routes.map { $0.completionRate }.reduce(0, +) / Double(routes.count)
        let failureCount = routes.flatMap { $0.actionNodes }.filter { $0.completionStatus == .failed }.count
        let totalActions = routes.flatMap { $0.actionNodes }.count
        
        let failureRate = totalActions > 0 ? Double(failureCount) / Double(totalActions) : 0
        
        switch (averageCompletionRate, failureRate) {
        case (0.8..., ...0.1):
            return .low
        case (0.6..<0.8, 0.1..<0.3):
            return .moderate
        case (0.4..<0.6, 0.3..<0.5):
            return .high
        default:
            return .critical
        }
    }
    
    // MARK: - Success Rate Curve
    private func calculateSuccessRateCurve(for routes: [Route]) -> [SuccessRateDataPoint] {
        let sortedRoutes = routes.sorted { $0.createdDate < $1.createdDate }
        var dataPoints: [SuccessRateDataPoint] = []
        var cumulativeTime: TimeInterval = 0
        
        for (index, route) in sortedRoutes.enumerated() {
            cumulativeTime += route.totalDuration
            let successfulRoutes = Array(sortedRoutes.prefix(index + 1)).filter { $0.completionRate >= 0.8 }.count
            let successRate = Double(successfulRoutes) / Double(index + 1)
            
            dataPoints.append(SuccessRateDataPoint(
                attemptNumber: index + 1,
                successRate: successRate,
                cumulativeTime: cumulativeTime
            ))
        }
        
        return dataPoints
    }
    
    // MARK: - Time Distribution
    private func calculateTimeDistribution(for routes: [Route]) -> [ActionType: TimeInterval] {
        var distribution: [ActionType: TimeInterval] = [:]

        for route in routes {
            for action in route.actionNodes {
                let time = action.timestamp.timeIntervalSince(route.createdDate)
                distribution[action.type, default: 0] += time
            }
        }

        return distribution
    }

    // MARK: - Improvement Suggestions
    private func generateImprovementSuggestions(from actionPerformances: [ActionPerformance]) -> [ImprovementSuggestion] {
        var suggestions: [ImprovementSuggestion] = []

        for performance in actionPerformances {
            if performance.failureRate > 0.3 {
                suggestions.append(contentsOf: getSuggestionsForAction(performance))
            }
        }

        return suggestions.sorted { $0.priority.rawValue < $1.priority.rawValue }
    }

    private func getSuggestionsForAction(_ performance: ActionPerformance) -> [ImprovementSuggestion] {
        let actionType = performance.actionType
        let failureRate = performance.failureRate
        let averageTime = performance.averageTime

        var suggestions: [ImprovementSuggestion] = []

        // High failure rate suggestions
        if failureRate > 0.5 {
            suggestions.append(ImprovementSuggestion(
                actionType: actionType,
                priority: .high,
                category: .technique,
                title: "Master \(actionType.displayName) Fundamentals",
                description: "Focus on basic technique and form for \(actionType.displayName) movements",
                expectedImprovement: "30-50% reduction in failure rate",
                difficulty: .medium
            ))
        }

        // Slow execution suggestions
        if averageTime > 15.0 {
            suggestions.append(ImprovementSuggestion(
                actionType: actionType,
                priority: .medium,
                category: .timing,
                title: "Improve \(actionType.displayName) Speed",
                description: "Practice explosive movements and reduce hesitation time",
                expectedImprovement: "20-30% time reduction",
                difficulty: .medium
            ))
        }

        // Action-specific suggestions
        switch actionType {
        case .jump:
            if failureRate > 0.4 {
                suggestions.append(ImprovementSuggestion(
                    actionType: actionType,
                    priority: .high,
                    category: .strength,
                    title: "Strengthen Leg Power",
                    description: "Focus on plyometric exercises and jump training",
                    expectedImprovement: "Improved jump distance and accuracy",
                    difficulty: .hard
                ))
            }
        case .climb:
            if failureRate > 0.3 {
                suggestions.append(ImprovementSuggestion(
                    actionType: actionType,
                    priority: .medium,
                    category: .strength,
                    title: "Build Upper Body Strength",
                    description: "Increase pull-up capacity and grip strength",
                    expectedImprovement: "Better climbing endurance",
                    difficulty: .hard
                ))
            }
        case .balance:
            if failureRate > 0.3 {
                suggestions.append(ImprovementSuggestion(
                    actionType: actionType,
                    priority: .medium,
                    category: .mental,
                    title: "Improve Balance Focus",
                    description: "Practice mindfulness and core stability exercises",
                    expectedImprovement: "Enhanced balance control",
                    difficulty: .easy
                ))
            }
        default:
            break
        }

        return suggestions
    }

    // MARK: - Risk Assessment
    private func assessRisks(from actionPerformances: [ActionPerformance]) -> RiskAssessment {
        var riskFactors: [RiskFactor] = []

        for performance in actionPerformances {
            if performance.failureRate > 0.4 {
                let severity: RiskLevel = performance.failureRate > 0.7 ? .critical : .high
                let riskFactor = RiskFactor(
                    actionType: performance.actionType,
                    description: "High failure rate in \(performance.actionType.displayName) actions",
                    severity: severity,
                    likelihood: performance.failureRate,
                    impact: 0.8,
                    mitigationSuggestion: "Focus on technique improvement and additional practice"
                )
                riskFactors.append(riskFactor)
            }
        }

        let overallRisk = determineOverallRisk(from: riskFactors)
        let mitigationStrategies = generateMitigationStrategies(for: riskFactors)

        return RiskAssessment(
            overallRisk: overallRisk,
            riskFactors: riskFactors,
            mitigationStrategies: mitigationStrategies
        )
    }

    private func determineOverallRisk(from riskFactors: [RiskFactor]) -> RiskLevel {
        if riskFactors.contains(where: { $0.severity == .critical }) {
            return .critical
        } else if riskFactors.contains(where: { $0.severity == .high }) {
            return .high
        } else if riskFactors.contains(where: { $0.severity == .moderate }) {
            return .moderate
        } else {
            return .low
        }
    }

    private func generateMitigationStrategies(for riskFactors: [RiskFactor]) -> [String] {
        var strategies: [String] = []

        if riskFactors.contains(where: { $0.actionType == .jump }) {
            strategies.append("Practice jump techniques on safer, lower obstacles first")
        }

        if riskFactors.contains(where: { $0.actionType == .climb }) {
            strategies.append("Use proper safety equipment and practice on easier routes")
        }

        if riskFactors.count > 3 {
            strategies.append("Consider breaking down the route into smaller, manageable sections")
        }

        strategies.append("Always warm up properly before attempting challenging routes")
        strategies.append("Practice failed actions in controlled environments")

        return strategies
    }

    // MARK: - Failure Reasons
    private func generateFailureReasons(for actionType: ActionType, successRate: Double) -> [String] {
        var reasons: [String] = []

        if successRate < 0.5 {
            switch actionType {
            case .jump:
                reasons.append("Insufficient leg power or poor timing")
                reasons.append("Incorrect takeoff angle or landing technique")
            case .climb:
                reasons.append("Lack of upper body strength or grip strength")
                reasons.append("Poor route planning or technique")
            case .balance:
                reasons.append("Core instability or lack of focus")
                reasons.append("Rushing through the movement")
            case .roll:
                reasons.append("Improper body positioning or momentum")
                reasons.append("Fear of impact affecting technique")
            default:
                reasons.append("Technical execution issues")
                reasons.append("Insufficient practice or conditioning")
            }
        }

        return reasons
    }

    private func generateActionSuggestions(for actionType: ActionType, stats: (attempts: Int, successes: Int, totalTime: TimeInterval, times: [TimeInterval])) -> [String] {
        var suggestions: [String] = []
        let successRate = Double(stats.successes) / Double(stats.attempts)

        if successRate < 0.7 {
            suggestions.append("Practice \(actionType.displayName) technique in controlled environment")
            suggestions.append("Break down the movement into smaller components")
        }

        if stats.totalTime / Double(stats.attempts) > 10.0 {
            suggestions.append("Work on movement fluidity and confidence")
            suggestions.append("Reduce hesitation time through repetitive practice")
        }

        return suggestions
    }
}
