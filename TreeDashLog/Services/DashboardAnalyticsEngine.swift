//
//  DashboardAnalyticsEngine.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import Foundation

class DashboardAnalyticsEngine {
    static let shared = DashboardAnalyticsEngine()
    
    private init() {}
    
    // MARK: - Main Dashboard Analysis
    func generateDashboardOverview() -> DashboardOverview {
        let routes = RouteManager.shared.getAllRoutes()
        
        let totalTrainingSessions = routes.count
        let totalRoutes = Set(routes.map { $0.name }).count
        let uniqueLocations = Set(routes.map { $0.location }).count
        let totalTrainingTime = routes.reduce(0) { $0 + $1.totalDuration }
        let overallSuccessRate = calculateOverallSuccessRate(routes: routes)
        let averageRouteCompletion = routes.map { $0.completionRate }.reduce(0, +) / Double(max(routes.count, 1))
        let mostActiveMonth = findMostActiveMonth(routes: routes)
        let streakData = calculateTrainingStreak(routes: routes)
        let lastTrainingDate = routes.max { $0.lastModified < $1.lastModified }?.lastModified
        
        return DashboardOverview(
            totalTrainingSessions: totalTrainingSessions,
            totalRoutes: totalRoutes,
            uniqueLocations: uniqueLocations,
            totalTrainingTime: totalTrainingTime,
            overallSuccessRate: overallSuccessRate,
            averageRouteCompletion: averageRouteCompletion,
            mostActiveMonth: mostActiveMonth,
            longestStreak: streakData.longestStreak,
            currentStreak: streakData.currentStreak,
            lastTrainingDate: lastTrainingDate
        )
    }
    
    // MARK: - Route Distribution Analysis
    func generateRouteDistribution() -> RouteDistribution {
        let routes = RouteManager.shared.getAllRoutes()
        
        var difficultyDistribution: [RouteDifficulty: Int] = [:]
        var locationDistribution: [String: Int] = [:]
        var actionTypeDistribution: [ActionType: Int] = [:]
        var monthlyDistribution: [String: Int] = [:]
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM"
        
        for route in routes {
            // Difficulty distribution
            difficultyDistribution[route.difficulty, default: 0] += 1
            
            // Location distribution
            locationDistribution[route.location, default: 0] += 1
            
            // Action type distribution
            for action in route.actionNodes {
                actionTypeDistribution[action.type, default: 0] += 1
            }
            
            // Monthly distribution
            let monthKey = dateFormatter.string(from: route.createdDate)
            monthlyDistribution[monthKey, default: 0] += 1
        }
        
        return RouteDistribution(
            difficultyDistribution: difficultyDistribution,
            locationDistribution: locationDistribution,
            actionTypeDistribution: actionTypeDistribution,
            monthlyDistribution: monthlyDistribution
        )
    }
    
    // MARK: - Success Rate Trends
    func generateSuccessRateTrend(timeUnit: SuccessRateTrend.TimeUnit) -> SuccessRateTrend {
        let routes = RouteManager.shared.getAllRoutes()
        let groupedRoutes = groupRoutesByTimeUnit(routes: routes, timeUnit: timeUnit)
        
        let dataPoints = groupedRoutes.map { (period, routesInPeriod) in
            let successRate = calculateSuccessRateForRoutes(routesInPeriod)
            let averageTime = routesInPeriod.map { $0.totalDuration }.reduce(0, +) / Double(max(routesInPeriod.count, 1))
            let firstDate = routesInPeriod.min { $0.createdDate < $1.createdDate }?.createdDate ?? Date()
            
            return TrendDataPoint(
                period: period,
                date: firstDate,
                successRate: successRate,
                totalAttempts: routesInPeriod.count,
                averageTime: averageTime,
                routeCount: routesInPeriod.count
            )
        }.sorted { $0.date < $1.date }
        
        return SuccessRateTrend(timeUnit: timeUnit, dataPoints: dataPoints)
    }
    
    // MARK: - Action Performance Analysis
    func generateActionPerformanceAnalysis() -> [ActionPerformanceAnalysis] {
        let routes = RouteManager.shared.getAllRoutes()
        var actionStats: [ActionType: (attempts: Int, successes: Int, totalTime: TimeInterval, timeHistory: [(Date, TimeInterval)])] = [:]
        
        for route in routes {
            for action in route.actionNodes {
                let key = action.type
                let isSuccess = action.completionStatus == .success
                let time = action.timestamp.timeIntervalSince(route.createdDate)
                
                if actionStats[key] == nil {
                    actionStats[key] = (0, 0, 0, [])
                }
                
                actionStats[key]!.attempts += 1
                if isSuccess {
                    actionStats[key]!.successes += 1
                }
                actionStats[key]!.totalTime += time
                actionStats[key]!.timeHistory.append((action.timestamp, time))
            }
        }
        
        return actionStats.map { (actionType, stats) in
            let averageTime = stats.attempts > 0 ? stats.totalTime / Double(stats.attempts) : 0
            let trend = calculatePerformanceTrend(timeHistory: stats.timeHistory)
            let failureReasons = generateFailureReasons(for: actionType, stats: stats)
            
            return ActionPerformanceAnalysis(
                actionType: actionType,
                totalAttempts: stats.attempts,
                successfulAttempts: stats.successes,
                failedAttempts: stats.attempts - stats.successes,
                averageTime: averageTime,
                trend: trend,
                commonFailureReasons: failureReasons
            )
        }.sorted { $0.failureRate > $1.failureRate }
    }
    
    // MARK: - Top Failures Analysis
    func generateTopFailuresAnalysis() -> TopFailuresAnalysis {
        let actionPerformances = generateActionPerformanceAnalysis()
        let topFailedActions = Array(actionPerformances.prefix(5)).map { performance in
            FailureAnalysis(
                actionType: performance.actionType,
                failureCount: performance.failedAttempts,
                failureRate: performance.failureRate,
                commonReasons: performance.commonFailureReasons,
                affectedRoutes: getAffectedRoutes(for: performance.actionType),
                trend: performance.trend
            )
        }
        
        let failurePatterns = identifyFailurePatterns(from: actionPerformances)
        let improvementOpportunities = generateImprovementOpportunities(from: topFailedActions)
        
        return TopFailuresAnalysis(
            topFailedActions: topFailedActions,
            failurePatterns: failurePatterns,
            improvementOpportunities: improvementOpportunities
        )
    }
    
    // MARK: - Historical Route Filtering
    func filterHistoricalRoutes(with filter: HistoricalRouteFilter) -> [Route] {
        let routes = RouteManager.shared.getAllRoutes()
        
        return routes.filter { route in
            // Location filter
            if !filter.selectedLocations.isEmpty && !filter.selectedLocations.contains(route.location) {
                return false
            }
            
            // Difficulty filter
            if !filter.selectedDifficulties.isEmpty && !filter.selectedDifficulties.contains(route.difficulty) {
                return false
            }
            
            // Date range filter
            if let dateRange = filter.dateRange {
                if !dateRange.contains(route.createdDate) {
                    return false
                }
            }
            
            // Success rate filter
            if let minRate = filter.minSuccessRate, route.completionRate < minRate {
                return false
            }
            
            if let maxRate = filter.maxSuccessRate, route.completionRate > maxRate {
                return false
            }
            
            return true
        }.sorted { route1, route2 in
            switch filter.sortBy {
            case .date:
                return filter.sortOrder == .ascending ? route1.createdDate < route2.createdDate : route1.createdDate > route2.createdDate
            case .name:
                return filter.sortOrder == .ascending ? route1.name < route2.name : route1.name > route2.name
            case .difficulty:
                return filter.sortOrder == .ascending ? route1.difficulty.rawValue < route2.difficulty.rawValue : route1.difficulty.rawValue > route2.difficulty.rawValue
            case .successRate:
                return filter.sortOrder == .ascending ? route1.completionRate < route2.completionRate : route1.completionRate > route2.completionRate
            case .duration:
                return filter.sortOrder == .ascending ? route1.totalDuration < route2.totalDuration : route1.totalDuration > route2.totalDuration
            }
        }
    }
    
    // MARK: - Training Streak Analysis
    func generateTrainingStreak() -> TrainingStreak {
        let routes = RouteManager.shared.getAllRoutes()
        let streakData = calculateTrainingStreak(routes: routes)
        let streakHistory = calculateStreakHistory(routes: routes)
        
        return TrainingStreak(
            currentStreak: streakData.currentStreak,
            longestStreak: streakData.longestStreak,
            streakStartDate: streakData.streakStartDate,
            lastTrainingDate: routes.max { $0.lastModified < $1.lastModified }?.lastModified,
            streakHistory: streakHistory
        )
    }
    
    // MARK: - Performance Milestones
    func generatePerformanceMilestones() -> [PerformanceMilestone] {
        let routes = RouteManager.shared.getAllRoutes()
        var milestones: [PerformanceMilestone] = []
        
        // Route count milestones
        let routeCount = routes.count
        let routeMilestones = [10, 25, 50, 100, 200]
        for milestone in routeMilestones {
            if routeCount >= milestone {
                if let achievedRoute = routes.sorted(by: { $0.createdDate < $1.createdDate }).dropFirst(milestone - 1).first {
                    milestones.append(PerformanceMilestone(
                        title: "\(milestone) Routes Completed",
                        description: "Reached \(milestone) total routes",
                        achievedDate: achievedRoute.createdDate,
                        category: .routes,
                        value: Double(milestone),
                        unit: "routes"
                    ))
                }
            }
        }
        
        // Success rate milestones
        let overallSuccessRate = calculateOverallSuccessRate(routes: routes)
        let successMilestones = [0.5, 0.7, 0.8, 0.9, 0.95]
        for milestone in successMilestones {
            if overallSuccessRate >= milestone {
                milestones.append(PerformanceMilestone(
                    title: "\(Int(milestone * 100))% Success Rate",
                    description: "Achieved \(Int(milestone * 100))% overall success rate",
                    achievedDate: Date(),
                    category: .success_rate,
                    value: milestone * 100,
                    unit: "%"
                ))
            }
        }
        
        return milestones.sorted { $0.achievedDate > $1.achievedDate }
    }

    // MARK: - Helper Methods
    private func calculateOverallSuccessRate(routes: [Route]) -> Double {
        guard !routes.isEmpty else { return 0.0 }
        let totalCompletionRate = routes.map { $0.completionRate }.reduce(0, +)
        return totalCompletionRate / Double(routes.count)
    }

    private func findMostActiveMonth(routes: [Route]) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM"

        var monthCounts: [String: Int] = [:]
        for route in routes {
            let monthKey = dateFormatter.string(from: route.createdDate)
            monthCounts[monthKey, default: 0] += 1
        }

        let mostActiveMonth = monthCounts.max { $0.value < $1.value }?.key ?? ""

        // Format for display
        if let date = dateFormatter.date(from: mostActiveMonth) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateFormat = "MMMM yyyy"
            return displayFormatter.string(from: date)
        }

        return "No data"
    }

    private func calculateTrainingStreak(routes: [Route]) -> (currentStreak: Int, longestStreak: Int, streakStartDate: Date?) {
        guard !routes.isEmpty else { return (0, 0, nil) }

        let sortedRoutes = routes.sorted { $0.createdDate < $1.createdDate }
        let calendar = Calendar.current

        var currentStreak = 0
        var longestStreak = 0
        var tempStreak = 1
        var streakStartDate: Date?

        for i in 1..<sortedRoutes.count {
            let previousDate = sortedRoutes[i-1].createdDate
            let currentDate = sortedRoutes[i].createdDate

            let daysDifference = calendar.dateComponents([.day], from: previousDate, to: currentDate).day ?? 0

            if daysDifference <= 7 { // Within a week
                tempStreak += 1
            } else {
                longestStreak = max(longestStreak, tempStreak)
                tempStreak = 1
            }
        }

        longestStreak = max(longestStreak, tempStreak)

        // Calculate current streak
        if let lastRoute = sortedRoutes.last {
            let daysSinceLastTraining = calendar.dateComponents([.day], from: lastRoute.createdDate, to: Date()).day ?? 0
            if daysSinceLastTraining <= 7 {
                currentStreak = tempStreak
                streakStartDate = lastRoute.createdDate
            }
        }

        return (currentStreak, longestStreak, streakStartDate)
    }

    private func groupRoutesByTimeUnit(routes: [Route], timeUnit: SuccessRateTrend.TimeUnit) -> [String: [Route]] {
        let dateFormatter = DateFormatter()

        switch timeUnit {
        case .week:
            dateFormatter.dateFormat = "yyyy-'W'ww"
        case .month:
            dateFormatter.dateFormat = "yyyy-MM"
        case .quarter:
            dateFormatter.dateFormat = "yyyy-'Q'Q"
        }

        var groupedRoutes: [String: [Route]] = [:]

        for route in routes {
            let key = dateFormatter.string(from: route.createdDate)
            groupedRoutes[key, default: []].append(route)
        }

        return groupedRoutes
    }

    private func calculateSuccessRateForRoutes(_ routes: [Route]) -> Double {
        guard !routes.isEmpty else { return 0.0 }
        let totalCompletionRate = routes.map { $0.completionRate }.reduce(0, +)
        return totalCompletionRate / Double(routes.count)
    }

    private func calculatePerformanceTrend(timeHistory: [(Date, TimeInterval)]) -> PerformanceTrend {
        guard timeHistory.count >= 3 else { return .insufficient_data }

        let sortedHistory = timeHistory.sorted { $0.0 < $1.0 }
        let firstHalf = Array(sortedHistory.prefix(sortedHistory.count / 2))
        let secondHalf = Array(sortedHistory.suffix(sortedHistory.count / 2))

        let firstHalfAverage = firstHalf.map { $0.1 }.reduce(0, +) / Double(firstHalf.count)
        let secondHalfAverage = secondHalf.map { $0.1 }.reduce(0, +) / Double(secondHalf.count)

        let improvementThreshold = firstHalfAverage * 0.1 // 10% improvement threshold

        if secondHalfAverage < firstHalfAverage - improvementThreshold {
            return .improving
        } else if secondHalfAverage > firstHalfAverage + improvementThreshold {
            return .declining
        } else {
            return .stable
        }
    }

    private func generateFailureReasons(for actionType: ActionType, stats: (attempts: Int, successes: Int, totalTime: TimeInterval, timeHistory: [(Date, TimeInterval)])) -> [String] {
        let failureRate = Double(stats.attempts - stats.successes) / Double(stats.attempts)

        var reasons: [String] = []

        if failureRate > 0.5 {
            switch actionType {
            case .jump:
                reasons.append("Insufficient leg power")
                reasons.append("Poor timing and distance judgment")
            case .climb:
                reasons.append("Lack of upper body strength")
                reasons.append("Poor grip technique")
            case .balance:
                reasons.append("Core instability")
                reasons.append("Lack of focus and concentration")
            case .roll:
                reasons.append("Improper body positioning")
                reasons.append("Fear of impact")
            default:
                reasons.append("Technical execution issues")
                reasons.append("Insufficient practice")
            }
        }

        return reasons
    }

    private func getAffectedRoutes(for actionType: ActionType) -> [String] {
        let routes = RouteManager.shared.getAllRoutes()
        return routes.filter { route in
            route.actionNodes.contains { $0.type == actionType && $0.completionStatus == .failed }
        }.map { $0.name }
    }

    private func identifyFailurePatterns(from performances: [ActionPerformanceAnalysis]) -> [FailurePattern] {
        var patterns: [FailurePattern] = []

        // Pattern 1: Multiple consecutive failures
        let highFailureActions = performances.filter { $0.failureRate > 0.4 }
        if highFailureActions.count >= 2 {
            patterns.append(FailurePattern(
                pattern: "Multiple high-failure actions",
                frequency: highFailureActions.count,
                affectedActions: highFailureActions.map { $0.actionType },
                severity: .high
            ))
        }

        // Pattern 2: Declining performance trend
        let decliningActions = performances.filter { $0.trend == .declining }
        if !decliningActions.isEmpty {
            patterns.append(FailurePattern(
                pattern: "Declining performance trend",
                frequency: decliningActions.count,
                affectedActions: decliningActions.map { $0.actionType },
                severity: .medium
            ))
        }

        return patterns
    }

    private func generateImprovementOpportunities(from failures: [FailureAnalysis]) -> [ImprovementOpportunity] {
        var opportunities: [ImprovementOpportunity] = []

        for failure in failures.prefix(3) {
            let opportunity = ImprovementOpportunity(
                title: "Improve \(failure.actionType.displayName) Technique",
                description: "Focus on addressing the \(String(format: "%.1f%%", failure.failureRate * 100)) failure rate in \(failure.actionType.displayName) actions",
                potentialImpact: "Could improve overall success rate by \(String(format: "%.1f%%", failure.failureRate * 20))",
                actionTypes: [failure.actionType],
                priority: failure.failureRate > 0.5 ? .high : .medium,
                estimatedTimeToImprove: failure.failureRate > 0.5 ? "2-4 weeks" : "1-2 weeks"
            )
            opportunities.append(opportunity)
        }

        return opportunities
    }

    private func calculateStreakHistory(routes: [Route]) -> [StreakPeriod] {
        guard !routes.isEmpty else { return [] }

        let sortedRoutes = routes.sorted { $0.createdDate < $1.createdDate }
        let calendar = Calendar.current
        var streakPeriods: [StreakPeriod] = []

        var streakStart = sortedRoutes[0].createdDate
        var streakLength = 1

        for i in 1..<sortedRoutes.count {
            let previousDate = sortedRoutes[i-1].createdDate
            let currentDate = sortedRoutes[i].createdDate

            let daysDifference = calendar.dateComponents([.day], from: previousDate, to: currentDate).day ?? 0

            if daysDifference <= 7 {
                streakLength += 1
            } else {
                if streakLength >= 2 {
                    streakPeriods.append(StreakPeriod(
                        startDate: streakStart,
                        endDate: previousDate,
                        streakLength: streakLength
                    ))
                }
                streakStart = currentDate
                streakLength = 1
            }
        }

        // Add the final streak if it's significant
        if streakLength >= 2 {
            streakPeriods.append(StreakPeriod(
                startDate: streakStart,
                endDate: sortedRoutes.last?.createdDate ?? Date(),
                streakLength: streakLength
            ))
        }

        return streakPeriods
    }
}
