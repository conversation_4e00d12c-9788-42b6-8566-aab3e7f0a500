//
//  UIColor+Theme.swift
//  TreeDashLog
//
//  Created by jj on 2025/6/28.
//

import UIKit

extension UIColor {
    
    // MARK: - TreeDash Purple Theme Colors
    static let treeDashPrimary = UIColor.systemPurple
    static let treeDashSecondary = UIColor(red: 0.7, green: 0.5, blue: 0.9, alpha: 1.0)
    static let treeDashAccent = UIColor(red: 0.5, green: 0.3, blue: 0.8, alpha: 1.0)
    static let treeDashLight = UIColor(red: 0.9, green: 0.85, blue: 0.95, alpha: 1.0)
    
    // MARK: - Status Colors
    static let successGreen = UIColor.systemGreen
    static let failureRed = UIColor.systemRed
    static let delayedOrange = UIColor.systemOrange
    static let notAttemptedGray = UIColor.systemGray
    
    // MARK: - Background Colors
    static let cardBackground = UIColor.secondarySystemGroupedBackground
    static let primaryBackground = UIColor.systemGroupedBackground
    
    // MARK: - Text Colors
    static let primaryText = UIColor.label
    static let secondaryText = UIColor.secondaryLabel
    static let accentText = UIColor.systemPurple
}

extension UIFont {
    
    // MARK: - TreeDash Typography
    static func treeDashTitle() -> UIFont {
        return .systemFont(ofSize: 24, weight: .bold)
    }
    
    static func treeDashHeadline() -> UIFont {
        return .systemFont(ofSize: 18, weight: .bold)
    }
    
    static func treeDashSubheadline() -> UIFont {
        return .systemFont(ofSize: 16, weight: .semibold)
    }
    
    static func treeDashBody() -> UIFont {
        return .systemFont(ofSize: 16, weight: .medium)
    }
    
    static func treeDashCaption() -> UIFont {
        return .systemFont(ofSize: 14, weight: .medium)
    }
    
    static func treeDashSmall() -> UIFont {
        return .systemFont(ofSize: 12, weight: .medium)
    }
}

extension UIView {
    
    // MARK: - TreeDash Card Styling
    func applyTreeDashCardStyle() {
        backgroundColor = .cardBackground
        layer.cornerRadius = 12
        layer.shadowColor = UIColor.black.cgColor
        layer.shadowOffset = CGSize(width: 0, height: 2)
        layer.shadowRadius = 4
        layer.shadowOpacity = 0.1
    }
    
    func applyTreeDashBorderStyle(color: UIColor = .treeDashPrimary, width: CGFloat = 1) {
        layer.borderColor = color.cgColor
        layer.borderWidth = width
        layer.cornerRadius = 8
    }
}

extension UIButton {
    
    // MARK: - TreeDash Button Styles
    func applyTreeDashPrimaryStyle() {
        backgroundColor = .treeDashPrimary
        setTitleColor(.white, for: .normal)
        layer.cornerRadius = 8
        titleLabel?.font = .treeDashSubheadline()
    }
    
    func applyTreeDashSecondaryStyle() {
        backgroundColor = .clear
        setTitleColor(.treeDashPrimary, for: .normal)
        layer.borderColor = UIColor.treeDashPrimary.cgColor
        layer.borderWidth = 1
        layer.cornerRadius = 8
        titleLabel?.font = .treeDashSubheadline()
    }
}
