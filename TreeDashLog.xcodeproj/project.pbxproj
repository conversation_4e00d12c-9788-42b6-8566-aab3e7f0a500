// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		28474C662E0F8B7200569E15 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 28474C5D2E0F8B7200569E15 /* Assets.xcassets */; };
		28474C682E0F8B7200569E15 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 28474C602E0F8B7200569E15 /* LaunchScreen.storyboard */; };
		28474C6A2E0F8B7200569E15 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C5C2E0F8B7200569E15 /* AppDelegate.swift */; };
		28474C7D2E0F901000569E15 /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C722E0F901000569E15 /* SceneDelegate.swift */; };
		28474C9C2E0F993500569E15 /* UIColor+Theme.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C852E0F993500569E15 /* UIColor+Theme.swift */; };
		28474C9D2E0F993500569E15 /* Route.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C882E0F993500569E15 /* Route.swift */; };
		28474C9E2E0F993500569E15 /* ActionNodeTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C952E0F993500569E15 /* ActionNodeTableViewCell.swift */; };
		28474C9F2E0F993500569E15 /* EfficiencyAnalysis.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C872E0F993500569E15 /* EfficiencyAnalysis.swift */; };
		28474CA02E0F993500569E15 /* SuccessRateChartView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C9A2E0F993500569E15 /* SuccessRateChartView.swift */; };
		28474CA12E0F993500569E15 /* SuccessRateCurveViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C932E0F993500569E15 /* SuccessRateCurveViewController.swift */; };
		28474CA22E0F993500569E15 /* EfficiencyCalculatorViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C8F2E0F993500569E15 /* EfficiencyCalculatorViewController.swift */; };
		28474CA32E0F993500569E15 /* RouteManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C892E0F993500569E15 /* RouteManager.swift */; };
		28474CA42E0F993500569E15 /* ActionTypeCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C972E0F993500569E15 /* ActionTypeCollectionViewCell.swift */; };
		28474CA52E0F993500569E15 /* RouteTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C992E0F993500569E15 /* RouteTableViewCell.swift */; };
		28474CA62E0F993500569E15 /* RouteListViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C922E0F993500569E15 /* RouteListViewController.swift */; };
		28474CA72E0F993500569E15 /* RouteDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C912E0F993500569E15 /* RouteDetailViewController.swift */; };
		28474CA82E0F993500569E15 /* ActionPerformanceTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C962E0F993500569E15 /* ActionPerformanceTableViewCell.swift */; };
		28474CA92E0F993500569E15 /* PerformanceAnalyticsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C902E0F993500569E15 /* PerformanceAnalyticsViewController.swift */; };
		28474CAA2E0F993500569E15 /* ActionDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C8D2E0F993500569E15 /* ActionDetailViewController.swift */; };
		28474CAB2E0F993500569E15 /* AnalyticsHistoryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C8E2E0F993500569E15 /* AnalyticsHistoryViewController.swift */; };
		28474CAC2E0F993500569E15 /* ImprovementSuggestionTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C982E0F993500569E15 /* ImprovementSuggestionTableViewCell.swift */; };
		28474CAD2E0F993500569E15 /* EfficiencyAnalysisEngine.swift in Sources */ = {isa = PBXBuildFile; fileRef = 28474C8B2E0F993500569E15 /* EfficiencyAnalysisEngine.swift */; };
		F0F4CC1A4C36FBC85B82226D /* Pods_TreeDashLog.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 49657E7B366CAB74FEFBE164 /* Pods_TreeDashLog.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		28474C442E0F8B6E00569E15 /* TreeDashLog.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TreeDashLog.app; sourceTree = BUILT_PRODUCTS_DIR; };
		28474C5C2E0F8B7200569E15 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		28474C5D2E0F8B7200569E15 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		28474C5E2E0F8B7200569E15 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		28474C5F2E0F8B7200569E15 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		28474C722E0F901000569E15 /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		28474C852E0F993500569E15 /* UIColor+Theme.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIColor+Theme.swift"; sourceTree = "<group>"; };
		28474C872E0F993500569E15 /* EfficiencyAnalysis.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EfficiencyAnalysis.swift; sourceTree = "<group>"; };
		28474C882E0F993500569E15 /* Route.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Route.swift; sourceTree = "<group>"; };
		28474C892E0F993500569E15 /* RouteManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RouteManager.swift; sourceTree = "<group>"; };
		28474C8B2E0F993500569E15 /* EfficiencyAnalysisEngine.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EfficiencyAnalysisEngine.swift; sourceTree = "<group>"; };
		28474C8D2E0F993500569E15 /* ActionDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActionDetailViewController.swift; sourceTree = "<group>"; };
		28474C8E2E0F993500569E15 /* AnalyticsHistoryViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnalyticsHistoryViewController.swift; sourceTree = "<group>"; };
		28474C8F2E0F993500569E15 /* EfficiencyCalculatorViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EfficiencyCalculatorViewController.swift; sourceTree = "<group>"; };
		28474C902E0F993500569E15 /* PerformanceAnalyticsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PerformanceAnalyticsViewController.swift; sourceTree = "<group>"; };
		28474C912E0F993500569E15 /* RouteDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RouteDetailViewController.swift; sourceTree = "<group>"; };
		28474C922E0F993500569E15 /* RouteListViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RouteListViewController.swift; sourceTree = "<group>"; };
		28474C932E0F993500569E15 /* SuccessRateCurveViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuccessRateCurveViewController.swift; sourceTree = "<group>"; };
		28474C952E0F993500569E15 /* ActionNodeTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActionNodeTableViewCell.swift; sourceTree = "<group>"; };
		28474C962E0F993500569E15 /* ActionPerformanceTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActionPerformanceTableViewCell.swift; sourceTree = "<group>"; };
		28474C972E0F993500569E15 /* ActionTypeCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ActionTypeCollectionViewCell.swift; sourceTree = "<group>"; };
		28474C982E0F993500569E15 /* ImprovementSuggestionTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImprovementSuggestionTableViewCell.swift; sourceTree = "<group>"; };
		28474C992E0F993500569E15 /* RouteTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RouteTableViewCell.swift; sourceTree = "<group>"; };
		28474C9A2E0F993500569E15 /* SuccessRateChartView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuccessRateChartView.swift; sourceTree = "<group>"; };
		49657E7B366CAB74FEFBE164 /* Pods_TreeDashLog.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_TreeDashLog.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		CD69AADA3561AC31CB4B208E /* Pods-TreeDashLog.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TreeDashLog.release.xcconfig"; path = "Target Support Files/Pods-TreeDashLog/Pods-TreeDashLog.release.xcconfig"; sourceTree = "<group>"; };
		EC615114E851FABE32CBCB90 /* Pods-TreeDashLog.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-TreeDashLog.debug.xcconfig"; path = "Target Support Files/Pods-TreeDashLog/Pods-TreeDashLog.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		28474C412E0F8B6E00569E15 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F0F4CC1A4C36FBC85B82226D /* Pods_TreeDashLog.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1B2A3F1D64C25611E327E086 /* Pods */ = {
			isa = PBXGroup;
			children = (
				EC615114E851FABE32CBCB90 /* Pods-TreeDashLog.debug.xcconfig */,
				CD69AADA3561AC31CB4B208E /* Pods-TreeDashLog.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		28474C3B2E0F8B6E00569E15 = {
			isa = PBXGroup;
			children = (
				28474C652E0F8B7200569E15 /* TreeDashLog */,
				28474C452E0F8B6E00569E15 /* Products */,
				1B2A3F1D64C25611E327E086 /* Pods */,
				62D5A7B1F96D6D06DBA97F7F /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		28474C452E0F8B6E00569E15 /* Products */ = {
			isa = PBXGroup;
			children = (
				28474C442E0F8B6E00569E15 /* TreeDashLog.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		28474C652E0F8B7200569E15 /* TreeDashLog */ = {
			isa = PBXGroup;
			children = (
				28474C862E0F993500569E15 /* Extensions */,
				28474C8A2E0F993500569E15 /* Models */,
				28474C8C2E0F993500569E15 /* Services */,
				28474C942E0F993500569E15 /* ViewControllers */,
				28474C9B2E0F993500569E15 /* Views */,
				28474C722E0F901000569E15 /* SceneDelegate.swift */,
				28474C5C2E0F8B7200569E15 /* AppDelegate.swift */,
				28474C5D2E0F8B7200569E15 /* Assets.xcassets */,
				28474C5E2E0F8B7200569E15 /* Info.plist */,
				28474C602E0F8B7200569E15 /* LaunchScreen.storyboard */,
			);
			path = TreeDashLog;
			sourceTree = "<group>";
		};
		28474C862E0F993500569E15 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				28474C852E0F993500569E15 /* UIColor+Theme.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		28474C8A2E0F993500569E15 /* Models */ = {
			isa = PBXGroup;
			children = (
				28474C872E0F993500569E15 /* EfficiencyAnalysis.swift */,
				28474C882E0F993500569E15 /* Route.swift */,
				28474C892E0F993500569E15 /* RouteManager.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		28474C8C2E0F993500569E15 /* Services */ = {
			isa = PBXGroup;
			children = (
				28474C8B2E0F993500569E15 /* EfficiencyAnalysisEngine.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		28474C942E0F993500569E15 /* ViewControllers */ = {
			isa = PBXGroup;
			children = (
				28474C8D2E0F993500569E15 /* ActionDetailViewController.swift */,
				28474C8E2E0F993500569E15 /* AnalyticsHistoryViewController.swift */,
				28474C8F2E0F993500569E15 /* EfficiencyCalculatorViewController.swift */,
				28474C902E0F993500569E15 /* PerformanceAnalyticsViewController.swift */,
				28474C912E0F993500569E15 /* RouteDetailViewController.swift */,
				28474C922E0F993500569E15 /* RouteListViewController.swift */,
				28474C932E0F993500569E15 /* SuccessRateCurveViewController.swift */,
			);
			path = ViewControllers;
			sourceTree = "<group>";
		};
		28474C9B2E0F993500569E15 /* Views */ = {
			isa = PBXGroup;
			children = (
				28474C952E0F993500569E15 /* ActionNodeTableViewCell.swift */,
				28474C962E0F993500569E15 /* ActionPerformanceTableViewCell.swift */,
				28474C972E0F993500569E15 /* ActionTypeCollectionViewCell.swift */,
				28474C982E0F993500569E15 /* ImprovementSuggestionTableViewCell.swift */,
				28474C992E0F993500569E15 /* RouteTableViewCell.swift */,
				28474C9A2E0F993500569E15 /* SuccessRateChartView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		62D5A7B1F96D6D06DBA97F7F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				49657E7B366CAB74FEFBE164 /* Pods_TreeDashLog.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		28474C432E0F8B6E00569E15 /* TreeDashLog */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 28474C572E0F8B6F00569E15 /* Build configuration list for PBXNativeTarget "TreeDashLog" */;
			buildPhases = (
				CC8ABE1EF6E3473A3EB6877D /* [CP] Check Pods Manifest.lock */,
				28474C402E0F8B6E00569E15 /* Sources */,
				28474C412E0F8B6E00569E15 /* Frameworks */,
				28474C422E0F8B6E00569E15 /* Resources */,
				E3242517507307A806586FF6 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = TreeDashLog;
			productName = TreeDashLog;
			productReference = 28474C442E0F8B6E00569E15 /* TreeDashLog.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		28474C3C2E0F8B6E00569E15 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					28474C432E0F8B6E00569E15 = {
						CreatedOnToolsVersion = 16.2;
					};
				};
			};
			buildConfigurationList = 28474C3F2E0F8B6E00569E15 /* Build configuration list for PBXProject "TreeDashLog" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 28474C3B2E0F8B6E00569E15;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 28474C452E0F8B6E00569E15 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				28474C432E0F8B6E00569E15 /* TreeDashLog */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		28474C422E0F8B6E00569E15 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28474C662E0F8B7200569E15 /* Assets.xcassets in Resources */,
				28474C682E0F8B7200569E15 /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		CC8ABE1EF6E3473A3EB6877D /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-TreeDashLog-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		E3242517507307A806586FF6 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TreeDashLog/Pods-TreeDashLog-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-TreeDashLog/Pods-TreeDashLog-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-TreeDashLog/Pods-TreeDashLog-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		28474C402E0F8B6E00569E15 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				28474C6A2E0F8B7200569E15 /* AppDelegate.swift in Sources */,
				28474C7D2E0F901000569E15 /* SceneDelegate.swift in Sources */,
				28474C9C2E0F993500569E15 /* UIColor+Theme.swift in Sources */,
				28474C9D2E0F993500569E15 /* Route.swift in Sources */,
				28474C9E2E0F993500569E15 /* ActionNodeTableViewCell.swift in Sources */,
				28474C9F2E0F993500569E15 /* EfficiencyAnalysis.swift in Sources */,
				28474CA02E0F993500569E15 /* SuccessRateChartView.swift in Sources */,
				28474CA12E0F993500569E15 /* SuccessRateCurveViewController.swift in Sources */,
				28474CA22E0F993500569E15 /* EfficiencyCalculatorViewController.swift in Sources */,
				28474CA32E0F993500569E15 /* RouteManager.swift in Sources */,
				28474CA42E0F993500569E15 /* ActionTypeCollectionViewCell.swift in Sources */,
				28474CA52E0F993500569E15 /* RouteTableViewCell.swift in Sources */,
				28474CA62E0F993500569E15 /* RouteListViewController.swift in Sources */,
				28474CA72E0F993500569E15 /* RouteDetailViewController.swift in Sources */,
				28474CA82E0F993500569E15 /* ActionPerformanceTableViewCell.swift in Sources */,
				28474CA92E0F993500569E15 /* PerformanceAnalyticsViewController.swift in Sources */,
				28474CAA2E0F993500569E15 /* ActionDetailViewController.swift in Sources */,
				28474CAB2E0F993500569E15 /* AnalyticsHistoryViewController.swift in Sources */,
				28474CAC2E0F993500569E15 /* ImprovementSuggestionTableViewCell.swift in Sources */,
				28474CAD2E0F993500569E15 /* EfficiencyAnalysisEngine.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		28474C602E0F8B7200569E15 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				28474C5F2E0F8B7200569E15 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		28474C582E0F8B6F00569E15 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = EC615114E851FABE32CBCB90 /* Pods-TreeDashLog.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = TreeDashLog/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = niucmd.TreeDashLog;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		28474C592E0F8B6F00569E15 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CD69AADA3561AC31CB4B208E /* Pods-TreeDashLog.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = TreeDashLog/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.chaungyuetrue.fd;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		28474C5A2E0F8B6F00569E15 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		28474C5B2E0F8B6F00569E15 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		28474C3F2E0F8B6E00569E15 /* Build configuration list for PBXProject "TreeDashLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				28474C5A2E0F8B6F00569E15 /* Debug */,
				28474C5B2E0F8B6F00569E15 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		28474C572E0F8B6F00569E15 /* Build configuration list for PBXNativeTarget "TreeDashLog" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				28474C582E0F8B6F00569E15 /* Debug */,
				28474C592E0F8B6F00569E15 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 28474C3C2E0F8B6E00569E15 /* Project object */;
}
